#!/usr/bin/env python3
"""
修正的时间戳验证测试 - 测试合理的历史时间戳
"""

import requests
import json
import time
from datetime import datetime, timedelta, timezone

def test_timestamp_validation():
    """测试时间戳验证的边界情况"""
    base_url = "http://localhost:8000"
    user_id = "timestamp_validation_test"
    
    print("🔍 时间戳验证边界测试:")
    
    # 获取当前时间
    current_time = datetime.now(timezone.utc)
    
    test_cases = [
        {
            "name": "1小时前（应该成功）",
            "timestamp": int((current_time - timedelta(hours=1)).timestamp()),
            "content": "One hour ago test",
            "should_succeed": True
        },
        {
            "name": "1天前（应该成功）", 
            "timestamp": int((current_time - timedelta(days=1)).timestamp()),
            "content": "One day ago test",
            "should_succeed": True
        },
        {
            "name": "1年前（应该成功）",
            "timestamp": int((current_time - timedelta(days=365)).timestamp()),
            "content": "One year ago test", 
            "should_succeed": True
        },
        {
            "name": "未来1小时（应该失败）",
            "timestamp": int((current_time + timedelta(hours=1)).timestamp()),
            "content": "Future test",
            "should_succeed": False
        },
        {
            "name": "毫秒格式 - 1天前（应该成功）",
            "timestamp": int((current_time - timedelta(days=1)).timestamp() * 1000),
            "content": "Milliseconds format test",
            "should_succeed": True
        }
    ]
    
    for test_case in test_cases:
        print(f"\n   测试: {test_case['name']}")
        print(f"   时间戳: {test_case['timestamp']}")
        
        # 构建请求
        url = f"{base_url}/v1/memories/"
        payload = {
            "messages": [{"role": "user", "content": test_case['content']}],
            "user_id": user_id,
            "timestamp": test_case['timestamp'],
            "version": "v1"
        }
        
        try:
            response = requests.post(url, json=payload)
            success = response.status_code == 200
            expected = test_case['should_succeed']
            
            # 检查是否符合预期
            if success == expected:
                status = "✅ 通过"
            else:
                status = "❌ 失败"
            
            print(f"   结果: {status} (期望: {'成功' if expected else '失败'}, 实际: {'成功' if success else '失败'})")
            
            if not success:
                print(f"   错误信息: {response.text}")
                
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")
        
        time.sleep(0.5)

if __name__ == "__main__":
    test_timestamp_validation()