/**
 * 测试脚本：验证系统资源监控修复
 * 
 * 这个脚本将在浏览器控制台中运行，验证监控数据的准确性
 */

console.log('🧪 开始测试系统资源监控修复...');

// 等待页面完全加载
setTimeout(() => {
  try {
    // 获取SystemMonitor实例
    const systemMonitor = window.SystemMonitor || (window as any).SystemMonitor;
    
    if (!systemMonitor) {
      console.error('❌ 无法找到SystemMonitor实例');
      return;
    }
    
    console.log('✅ 找到SystemMonitor实例');
    
    // 测试1: 检查内存数据一致性
    console.log('\n📊 测试1: 内存数据一致性');
    const resourceUsage = systemMonitor.getResourceUsage();
    
    if (resourceUsage && resourceUsage.memory) {
      const { used, total, percentage } = resourceUsage.memory;
      const calculatedPercentage = Math.round((used / total) * 100);
      
      console.log(`内存使用: ${used}MB / ${total}MB`);
      console.log(`显示百分比: ${percentage}%`);
      console.log(`计算百分比: ${calculatedPercentage}%`);
      
      if (Math.abs(percentage - calculatedPercentage) <= 1) {
        console.log('✅ 内存数据一致性测试通过');
      } else {
        console.log('❌ 内存数据一致性测试失败');
      }
    }
    
    // 测试2: 检查CPU数据合理性
    console.log('\n🖥️ 测试2: CPU数据合理性');
    if (resourceUsage && resourceUsage.cpu) {
      const { usage, cores } = resourceUsage.cpu;
      
      console.log(`CPU使用率: ${usage}%`);
      console.log(`CPU核心数: ${cores}`);
      
      if (usage >= 0 && usage <= 100 && cores > 0) {
        console.log('✅ CPU数据合理性测试通过');
      } else {
        console.log('❌ CPU数据合理性测试失败');
      }
    }
    
    // 测试3: 检查网络数据准确性
    console.log('\n🌐 测试3: 网络数据准确性');
    if (resourceUsage && resourceUsage.network) {
      const { requests, errors, bandwidth } = resourceUsage.network;
      
      console.log(`网络请求: ${requests}`);
      console.log(`网络错误: ${errors}`);
      console.log(`带宽使用: ${bandwidth} KB/s`);
      
      if (requests >= 0 && errors >= 0 && errors <= requests && bandwidth >= 0) {
        console.log('✅ 网络数据准确性测试通过');
      } else {
        console.log('❌ 网络数据准确性测试失败');
      }
    }
    
    // 测试4: 检查数据更新频率
    console.log('\n⏱️ 测试4: 数据更新频率');
    const initialTimestamp = resourceUsage ? resourceUsage.timestamp : 0;
    
    setTimeout(() => {
      const newResourceUsage = systemMonitor.getResourceUsage();
      const newTimestamp = newResourceUsage ? newResourceUsage.timestamp : 0;
      
      if (newTimestamp > initialTimestamp) {
        console.log('✅ 数据更新频率测试通过');
      } else {
        console.log('❌ 数据更新频率测试失败');
      }
      
      // 测试5: 检查浏览器内存API可用性
      console.log('\n🧠 测试5: 浏览器内存API可用性');
      if (typeof performance !== 'undefined' && 'memory' in performance) {
        const memInfo = (performance as any).memory;
        console.log('浏览器内存信息:', {
          usedJSHeapSize: `${Math.round(memInfo.usedJSHeapSize / (1024 * 1024))}MB`,
          totalJSHeapSize: `${Math.round(memInfo.totalJSHeapSize / (1024 * 1024))}MB`,
          jsHeapSizeLimit: `${Math.round(memInfo.jsHeapSizeLimit / (1024 * 1024))}MB`
        });
        console.log('✅ 浏览器内存API可用');
      } else {
        console.log('❌ 浏览器内存API不可用');
      }
      
      console.log('\n🎉 系统资源监控测试完成！');
      
    }, 12000); // 等待12秒，确保数据更新
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}, 2000); // 等待2秒让页面完全加载
