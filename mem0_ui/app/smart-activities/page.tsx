'use client';

import React from 'react';
import SmartActivityTimeline from '@/components/mem0/SmartActivityTimeline';

export default function SmartActivitiesPage() {
  return (
    <div className="min-h-screen bg-black text-white">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">
            智能活动时间线
          </h1>
          <p className="text-zinc-400 mb-4">
            自动过滤不必要的操作数据，专注于重要的用户活动
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-zinc-900 border border-zinc-800 rounded-lg p-4">
              <h3 className="text-sm font-medium text-[#00d4aa] mb-2">🎯 智能过滤</h3>
              <p className="text-xs text-zinc-400">
                自动识别并隐藏UI操作、系统操作、重复搜索等不必要的数据
              </p>
            </div>
            
            <div className="bg-zinc-900 border border-zinc-800 rounded-lg p-4">
              <h3 className="text-sm font-medium text-[#00d4aa] mb-2">🔧 可配置</h3>
              <p className="text-xs text-zinc-400">
                支持自定义过滤规则、时间范围、关键词排除等多种过滤选项
              </p>
            </div>
            
            <div className="bg-zinc-900 border border-zinc-800 rounded-lg p-4">
              <h3 className="text-sm font-medium text-[#00d4aa] mb-2">📊 统计信息</h3>
              <p className="text-xs text-zinc-400">
                实时显示过滤前后的数据统计，帮助了解过滤效果
              </p>
            </div>
          </div>
        </div>

        {/* 智能活动时间线 */}
        <SmartActivityTimeline 
          dataSource="mock" 
          autoRefresh={false}
        />

        <div className="mt-8 space-y-6">
          {/* 功能说明 */}
          <div className="bg-zinc-900 border border-zinc-800 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-white mb-4">🚀 主要功能</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-medium text-[#00d4aa] mb-3">自动过滤规则</h3>
                <ul className="text-sm text-zinc-400 space-y-2">
                  <li>• <strong>UI操作过滤：</strong>自动隐藏页面刷新、加载、路由切换等UI操作</li>
                  <li>• <strong>系统操作过滤：</strong>隐藏心跳检测、健康检查、维护任务等系统操作</li>
                  <li>• <strong>重复搜索过滤：</strong>识别并隐藏相同用户的重复搜索操作</li>
                  <li>• <strong>空数据过滤：</strong>隐藏没有metadata或数据不完整的记录</li>
                </ul>
              </div>
              
              <div>
                <h3 className="font-medium text-[#00d4aa] mb-3">高级过滤选项</h3>
                <ul className="text-sm text-zinc-400 space-y-2">
                  <li>• <strong>操作类型选择：</strong>可选择显示/隐藏特定类型的操作</li>
                  <li>• <strong>时间范围过滤：</strong>支持1小时、24小时、7天、30天等时间范围</li>
                  <li>• <strong>关键词过滤：</strong>支持排除或包含特定关键词的活动</li>
                  <li>• <strong>用户过滤：</strong>可过滤特定用户或隐藏匿名用户</li>
                </ul>
              </div>
            </div>
          </div>

          {/* 使用场景 */}
          <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-blue-400 mb-4">💡 解决的问题</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-medium text-blue-300 mb-3">❌ 之前的问题</h3>
                <ul className="text-sm text-blue-200 space-y-2">
                  <li>• 页面操作产生大量不需要的数据</li>
                  <li>• 每次刷新、搜索都会记录到活动中</li>
                  <li>• 真正重要的用户操作被淹没</li>
                  <li>• 活动列表充满了噪音数据</li>
                  <li>• 难以找到有价值的操作记录</li>
                </ul>
              </div>
              
              <div>
                <h3 className="font-medium text-blue-300 mb-3">✅ 现在的解决方案</h3>
                <ul className="text-sm text-blue-200 space-y-2">
                  <li>• 智能识别并过滤UI操作</li>
                  <li>• 只显示真正有意义的用户活动</li>
                  <li>• 可配置的过滤规则</li>
                  <li>• 清晰的数据统计和过滤效果</li>
                  <li>• 专注于重要的业务操作</li>
                </ul>
              </div>
            </div>
          </div>

          {/* 过滤示例 */}
          <div className="bg-zinc-900 border border-zinc-800 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-white mb-4">📋 过滤示例</h2>
            
            <div className="space-y-4">
              <div className="bg-zinc-800/50 rounded-lg p-4">
                <h3 className="font-medium text-red-400 mb-2">🚫 会被过滤的操作</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-zinc-300 mb-1"><strong>UI操作：</strong></p>
                    <ul className="text-zinc-400 space-y-1">
                      <li>• page_load: 自动搜索记忆</li>
                      <li>• ui_refresh: 刷新活动列表</li>
                      <li>• route_change: 页面路由切换</li>
                      <li>• filter_change: 过滤器变更</li>
                    </ul>
                  </div>
                  <div>
                    <p className="text-zinc-300 mb-1"><strong>系统操作：</strong></p>
                    <ul className="text-zinc-400 space-y-1">
                      <li>• heartbeat: 系统心跳检测</li>
                      <li>• health_check: 健康状态检查</li>
                      <li>• cleanup: 数据清理任务</li>
                      <li>• backup: 自动备份操作</li>
                    </ul>
                  </div>
                </div>
              </div>
              
              <div className="bg-zinc-800/50 rounded-lg p-4">
                <h3 className="font-medium text-green-400 mb-2">✅ 会保留的操作</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-zinc-300 mb-1"><strong>用户操作：</strong></p>
                    <ul className="text-zinc-400 space-y-1">
                      <li>• 用户主动添加记忆</li>
                      <li>• 用户手动搜索记忆</li>
                      <li>• 用户更新记忆内容</li>
                      <li>• 用户删除记忆</li>
                    </ul>
                  </div>
                  <div>
                    <p className="text-zinc-300 mb-1"><strong>业务操作：</strong></p>
                    <ul className="text-zinc-400 space-y-1">
                      <li>• API调用产生的记忆</li>
                      <li>• 批量导入记忆</li>
                      <li>• 记忆分类和标记</li>
                      <li>• 重要的配置变更</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
