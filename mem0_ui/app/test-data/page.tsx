'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { RefreshCw } from 'lucide-react';

export default function TestDataPage() {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('http://localhost:8000/v1/activities/?limit=10');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const result = await response.json();
      setData(result);
      console.log('获取到的数据:', result);
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取数据失败');
      console.error('获取数据错误:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div className="min-h-screen bg-black text-white p-8">
      <div className="container mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-white mb-2">数据测试页面</h1>
          <p className="text-zinc-400">检查API数据是否正常返回</p>
        </div>

        <Card className="bg-zinc-900 border-zinc-800 mb-6">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-white">API响应数据</CardTitle>
              <Button
                variant="outline"
                size="sm"
                onClick={fetchData}
                disabled={loading}
                className="border-zinc-700 hover:border-zinc-600"
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                刷新
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {loading && (
              <div className="text-zinc-400">正在加载数据...</div>
            )}
            
            {error && (
              <div className="text-red-400">错误: {error}</div>
            )}
            
            {data && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-zinc-800 rounded-lg p-4">
                    <h3 className="text-[#00d4aa] font-medium mb-2">总记录数</h3>
                    <p className="text-2xl font-bold">{data.total}</p>
                  </div>
                  <div className="bg-zinc-800 rounded-lg p-4">
                    <h3 className="text-[#00d4aa] font-medium mb-2">当前页记录</h3>
                    <p className="text-2xl font-bold">{data.activities?.length || 0}</p>
                  </div>
                  <div className="bg-zinc-800 rounded-lg p-4">
                    <h3 className="text-[#00d4aa] font-medium mb-2">是否有更多</h3>
                    <p className="text-2xl font-bold">{data.has_more ? '是' : '否'}</p>
                  </div>
                </div>

                <div className="bg-zinc-800 rounded-lg p-4">
                  <h3 className="text-[#00d4aa] font-medium mb-4">前5条记录详情</h3>
                  <div className="space-y-3">
                    {data.activities?.slice(0, 5).map((activity: any, index: number) => (
                      <div key={activity.id} className="border border-zinc-700 rounded p-3">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                          <div>
                            <span className="text-zinc-400">ID:</span>
                            <span className="ml-2 font-mono text-xs">{activity.id}</span>
                          </div>
                          <div>
                            <span className="text-zinc-400">时间:</span>
                            <span className="ml-2">{new Date(activity.timestamp).toLocaleString()}</span>
                          </div>
                          <div>
                            <span className="text-zinc-400">操作:</span>
                            <span className="ml-2 text-[#00d4aa]">{activity.operation}</span>
                          </div>
                          <div>
                            <span className="text-zinc-400">状态:</span>
                            <span className="ml-2 text-green-400">{activity.status}</span>
                          </div>
                          <div>
                            <span className="text-zinc-400">用户ID:</span>
                            <span className="ml-2">{activity.user_id || '(null)'}</span>
                          </div>
                          <div>
                            <span className="text-zinc-400">记忆ID:</span>
                            <span className="ml-2 font-mono text-xs">{activity.memory_id}</span>
                          </div>
                          <div className="md:col-span-2">
                            <span className="text-zinc-400">详情:</span>
                            <span className="ml-2">{activity.details}</span>
                          </div>
                          <div className="md:col-span-2">
                            <span className="text-zinc-400">元数据:</span>
                            <span className="ml-2">{activity.metadata ? JSON.stringify(activity.metadata) : '(null)'}</span>
                          </div>
                          <div className="md:col-span-2">
                            <span className="text-zinc-400">响应时间:</span>
                            <span className="ml-2">{activity.response_time || '(null)'}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="bg-zinc-800 rounded-lg p-4">
                  <h3 className="text-[#00d4aa] font-medium mb-2">完整JSON响应</h3>
                  <pre className="text-xs text-zinc-300 overflow-auto max-h-96 bg-zinc-900 p-3 rounded">
                    {JSON.stringify(data, null, 2)}
                  </pre>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
