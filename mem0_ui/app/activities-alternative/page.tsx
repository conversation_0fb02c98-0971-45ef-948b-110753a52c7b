'use client';

import React from 'react';
import ActivityTimelineAlternative from '@/components/mem0/ActivityTimelineAlternative';
import RealTimeActivityMonitor from '@/components/mem0/RealTimeActivityMonitor';

export default function ActivitiesAlternativePage() {
  return (
    <div className="min-h-screen bg-black text-white">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">
            活动时间线 - 多数据源版本
          </h1>
          <p className="text-zinc-400">
            演示不同数据获取方式的活动时间线组件
          </p>
        </div>

        <div className="space-y-6">
          {/* 实时监控组件 */}
          <RealTimeActivityMonitor maxActivities={20} />

          {/* 多数据源活动时间线 */}
          <ActivityTimelineAlternative dataSource="database" />

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ActivityTimelineAlternative dataSource="memory_api" />
            <ActivityTimelineAlternative dataSource="mock" />
          </div>
        </div>

        <div className="mt-8 p-6 bg-zinc-900 border border-zinc-800 rounded-lg">
          <h2 className="text-xl font-semibold text-white mb-4">数据源说明</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h3 className="font-medium text-[#00d4aa] mb-2">1. 数据库API (当前方式)</h3>
              <ul className="text-zinc-400 space-y-1">
                <li>• 从SQLite历史数据库获取</li>
                <li>• 包含完整的操作历史</li>
                <li>• 需要后端正确配置</li>
                <li>• 最准确和完整的数据</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-medium text-[#00d4aa] mb-2">2. 记忆API推断</h3>
              <ul className="text-zinc-400 space-y-1">
                <li>• 从记忆API推断活动</li>
                <li>• 基于现有记忆生成ADD活动</li>
                <li>• 模拟搜索和其他操作</li>
                <li>• 不依赖历史数据库</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-medium text-[#00d4aa] mb-2">3. 本地存储</h3>
              <ul className="text-zinc-400 space-y-1">
                <li>• 使用浏览器localStorage</li>
                <li>• 作为其他数据源的备份</li>
                <li>• 离线可用</li>
                <li>• 数据持久化在客户端</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-medium text-[#00d4aa] mb-2">4. 模拟数据</h3>
              <ul className="text-zinc-400 space-y-1">
                <li>• 生成随机的活动数据</li>
                <li>• 用于开发和测试</li>
                <li>• 不依赖任何外部服务</li>
                <li>• 展示UI组件功能</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="mt-6 p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg">
          <h3 className="font-medium text-blue-400 mb-2">💡 使用建议</h3>
          <div className="text-sm text-blue-300">
            <p className="mb-2">
              <strong>生产环境：</strong> 优先使用"数据库API"，确保OpenAI API密钥正确配置
            </p>
            <p className="mb-2">
              <strong>开发环境：</strong> 可以使用"模拟数据"进行UI开发和测试
            </p>
            <p>
              <strong>降级策略：</strong> 当数据库API不可用时，自动切换到"本地存储"备份数据
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
