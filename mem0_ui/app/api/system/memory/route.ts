import { NextRequest, NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

/**
 * 获取系统内存信息的API端点
 * 通过读取 /proc/meminfo 获取真实的LXC容器内存使用情况
 */
export async function GET(request: NextRequest) {
  try {
    // 读取 /proc/meminfo 获取内存信息
    const { stdout } = await execAsync('cat /proc/meminfo');
    
    const memInfo = parseMemInfo(stdout);
    
    return NextResponse.json({
      success: true,
      data: {
        total: memInfo.total,
        used: memInfo.used,
        free: memInfo.free,
        available: memInfo.available,
        percentage: Math.round((memInfo.used / memInfo.total) * 100),
        timestamp: Date.now()
      }
    });
    
  } catch (error) {
    console.error('获取内存信息失败:', error);
    
    // 如果无法读取系统信息，返回默认值
    return NextResponse.json({
      success: false,
      error: '无法获取系统内存信息',
      data: {
        total: 8 * 1024 * 1024 * 1024, // 8GB in bytes
        used: 3 * 1024 * 1024 * 1024,  // 3GB in bytes
        free: 5 * 1024 * 1024 * 1024,  // 5GB in bytes
        available: 5 * 1024 * 1024 * 1024, // 5GB in bytes
        percentage: 37,
        timestamp: Date.now()
      }
    });
  }
}

/**
 * 解析 /proc/meminfo 的输出
 */
function parseMemInfo(memInfoOutput: string): {
  total: number;
  used: number;
  free: number;
  available: number;
} {
  const lines = memInfoOutput.split('\n');
  const memData: { [key: string]: number } = {};
  
  // 解析每一行
  lines.forEach(line => {
    const match = line.match(/^(\w+):\s*(\d+)\s*kB/);
    if (match) {
      const key = match[1];
      const value = parseInt(match[2]) * 1024; // 转换为字节
      memData[key] = value;
    }
  });
  
  // 计算内存使用情况
  const total = memData.MemTotal || 0;
  const free = memData.MemFree || 0;
  const buffers = memData.Buffers || 0;
  const cached = memData.Cached || 0;
  const sReclaimable = memData.SReclaimable || 0;
  const available = memData.MemAvailable || (free + buffers + cached + sReclaimable);
  
  // 计算已使用内存
  const used = total - available;
  
  return {
    total,
    used: Math.max(0, used),
    free,
    available
  };
}
