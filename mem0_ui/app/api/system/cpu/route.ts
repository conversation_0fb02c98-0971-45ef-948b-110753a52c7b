import { NextRequest, NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';
import { readFile } from 'fs/promises';

const execAsync = promisify(exec);

/**
 * 获取系统CPU信息的API端点
 * 通过读取 /proc/stat 和 /proc/cpuinfo 获取真实的LXC容器CPU使用情况
 */
export async function GET(request: NextRequest) {
  try {
    // 获取CPU核心数
    const cores = await getCPUCores();
    
    // 获取CPU使用率
    const usage = await getCPUUsage();
    
    return NextResponse.json({
      success: true,
      data: {
        cores,
        usage: Math.round(usage),
        timestamp: Date.now()
      }
    });
    
  } catch (error) {
    console.error('获取CPU信息失败:', error);
    
    // 如果无法读取系统信息，返回默认值
    return NextResponse.json({
      success: false,
      error: '无法获取系统CPU信息',
      data: {
        cores: 4,
        usage: 25,
        timestamp: Date.now()
      }
    });
  }
}

/**
 * 获取CPU核心数
 */
async function getCPUCores(): Promise<number> {
  try {
    const cpuInfo = await readFile('/proc/cpuinfo', 'utf8');
    const processors = cpuInfo.match(/^processor\s*:/gm);
    return processors ? processors.length : 4;
  } catch (error) {
    // 尝试通过 nproc 命令获取
    try {
      const { stdout } = await execAsync('nproc');
      return parseInt(stdout.trim()) || 4;
    } catch {
      return 4; // 默认4核心
    }
  }
}

/**
 * 获取CPU使用率
 * 通过读取两次 /proc/stat 计算CPU使用率
 */
async function getCPUUsage(): Promise<number> {
  try {
    // 第一次读取
    const stat1 = await readCPUStat();
    
    // 等待100ms
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // 第二次读取
    const stat2 = await readCPUStat();
    
    // 计算CPU使用率
    const totalDiff = stat2.total - stat1.total;
    const idleDiff = stat2.idle - stat1.idle;
    
    if (totalDiff === 0) {
      return 0;
    }
    
    const usage = ((totalDiff - idleDiff) / totalDiff) * 100;
    return Math.max(0, Math.min(100, usage));
    
  } catch (error) {
    console.error('计算CPU使用率失败:', error);
    return 25; // 默认25%使用率
  }
}

/**
 * 读取 /proc/stat 中的CPU统计信息
 */
async function readCPUStat(): Promise<{ total: number; idle: number }> {
  const stat = await readFile('/proc/stat', 'utf8');
  const cpuLine = stat.split('\n')[0]; // 第一行是总CPU统计
  
  // 解析CPU时间：user nice system idle iowait irq softirq steal guest guest_nice
  const values = cpuLine.split(/\s+/).slice(1).map(Number);
  
  const user = values[0] || 0;
  const nice = values[1] || 0;
  const system = values[2] || 0;
  const idle = values[3] || 0;
  const iowait = values[4] || 0;
  const irq = values[5] || 0;
  const softirq = values[6] || 0;
  const steal = values[7] || 0;
  
  const total = user + nice + system + idle + iowait + irq + softirq + steal;
  
  return { total, idle };
}
