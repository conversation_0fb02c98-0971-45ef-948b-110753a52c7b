'use client';

import React, { useState, useEffect } from 'react';
import { Clock, Activity, User, Database, Zap, CheckCircle, Play, RefreshCw } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import CollapsibleMetadata from './CollapsibleMetadata';

// 活动数据类型
interface ActivityItem {
  id: string;
  timestamp: string;
  operation: 'ADD' | 'SEARCH' | 'UPDATE' | 'DELETE';
  details: string;
  user_id?: string;
  memory_id?: string;
  metadata?: any;
  response_time?: string;
  status: 'success' | 'error' | 'pending';
}

// 数据源类型
type DataSource = 'database' | 'memory_api' | 'local_storage' | 'mock';

interface ActivityTimelineAlternativeProps {
  dataSource?: DataSource;
  userId?: string;
}

const ActivityTimelineAlternative: React.FC<ActivityTimelineAlternativeProps> = ({
  dataSource = 'database',
  userId
}) => {
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentDataSource, setCurrentDataSource] = useState<DataSource>(dataSource);

  // 方案1: 从数据库API获取 (当前方式)
  const fetchFromDatabase = async (): Promise<ActivityItem[]> => {
    const response = await fetch('http://localhost:8000/v1/activities/?limit=50');
    if (!response.ok) throw new Error('Database API failed');
    const data = await response.json();
    return data.activities || [];
  };

  // 方案2: 从记忆API推断活动
  const fetchFromMemoryAPI = async (): Promise<ActivityItem[]> => {
    const activities: ActivityItem[] = [];
    
    try {
      // 获取最近的记忆
      const memoryResponse = await fetch('http://localhost:8000/v1/memories/?limit=20');
      if (memoryResponse.ok) {
        const memoryData = await memoryResponse.json();
        
        // 为每个记忆创建ADD活动
        memoryData.memories?.forEach((memory: any, index: number) => {
          activities.push({
            id: `mem_${memory.id}`,
            timestamp: memory.created_at || new Date().toISOString(),
            operation: 'ADD',
            details: `创建记忆: ${memory.memory?.substring(0, 50)}...`,
            user_id: memory.user_id,
            memory_id: memory.id,
            metadata: {
              memory_type: 'user',
              content_length: memory.memory?.length || 0,
              categories: memory.custom_categories || []
            },
            status: 'success'
          });
        });
      }

      // 模拟一些搜索活动
      for (let i = 0; i < 5; i++) {
        activities.push({
          id: `search_${Date.now()}_${i}`,
          timestamp: new Date(Date.now() - i * 300000).toISOString(),
          operation: 'SEARCH',
          details: '执行记忆搜索',
          user_id: userId || 'current_user',
          metadata: {
            query_type: 'semantic',
            results_count: Math.floor(Math.random() * 20) + 1,
            index_used: 'vector'
          },
          response_time: `${Math.floor(Math.random() * 500) + 50}ms`,
          status: 'success'
        });
      }

    } catch (error) {
      console.error('Error fetching from memory API:', error);
    }

    return activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  };

  // 方案3: 从本地存储获取
  const fetchFromLocalStorage = (): ActivityItem[] => {
    try {
      const stored = localStorage.getItem('mem0_activities');
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.error('Error reading from localStorage:', error);
    }
    return [];
  };

  // 方案4: 模拟数据
  const fetchMockData = (): ActivityItem[] => {
    const mockActivities: ActivityItem[] = [];
    const operations: Array<'ADD' | 'SEARCH' | 'UPDATE' | 'DELETE'> = ['ADD', 'SEARCH', 'UPDATE', 'DELETE'];
    
    for (let i = 0; i < 15; i++) {
      const operation = operations[Math.floor(Math.random() * operations.length)];
      const timestamp = new Date(Date.now() - i * 600000).toISOString();
      
      mockActivities.push({
        id: `mock_${i}`,
        timestamp,
        operation,
        details: `模拟${operation}操作`,
        user_id: userId || `user_${Math.floor(Math.random() * 100)}`,
        memory_id: `memory_${Math.floor(Math.random() * 1000)}`,
        metadata: {
          source: 'mock',
          operation_type: operation.toLowerCase(),
          timestamp: timestamp,
          ...(operation === 'SEARCH' && {
            query_type: 'semantic',
            results_count: Math.floor(Math.random() * 20) + 1,
            index_used: 'vector'
          }),
          ...(operation === 'ADD' && {
            memory_type: 'user',
            content_length: Math.floor(Math.random() * 200) + 50,
            embedding_model: 'text-embedding-ada-002'
          })
        },
        response_time: `${Math.floor(Math.random() * 500) + 50}ms`,
        status: 'success'
      });
    }
    
    return mockActivities;
  };

  // 保存到本地存储
  const saveToLocalStorage = (activities: ActivityItem[]) => {
    try {
      localStorage.setItem('mem0_activities', JSON.stringify(activities));
    } catch (error) {
      console.error('Error saving to localStorage:', error);
    }
  };

  // 根据数据源获取数据
  const fetchActivities = async () => {
    setLoading(true);
    setError(null);

    try {
      let data: ActivityItem[] = [];

      switch (currentDataSource) {
        case 'database':
          data = await fetchFromDatabase();
          break;
        case 'memory_api':
          data = await fetchFromMemoryAPI();
          break;
        case 'local_storage':
          data = fetchFromLocalStorage();
          break;
        case 'mock':
          data = fetchMockData();
          break;
      }

      setActivities(data);
      
      // 如果是从API获取的数据，保存到本地存储作为备份
      if (currentDataSource !== 'local_storage' && data.length > 0) {
        saveToLocalStorage(data);
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取活动数据失败';
      setError(errorMessage);
      
      // 如果主要数据源失败，尝试从本地存储获取备份数据
      if (currentDataSource !== 'local_storage') {
        const backupData = fetchFromLocalStorage();
        if (backupData.length > 0) {
          setActivities(backupData);
          setError(`${errorMessage} (显示缓存数据)`);
        }
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchActivities();
  }, [currentDataSource]);

  // 获取操作信息
  const getOperationInfo = (operation: string) => {
    const operationMap = {
      'ADD': { text: '添加', color: 'bg-green-500/20 text-green-400 border-green-500/30' },
      'SEARCH': { text: '搜索', color: 'bg-blue-500/20 text-blue-400 border-blue-500/30' },
      'UPDATE': { text: '更新', color: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30' },
      'DELETE': { text: '删除', color: 'bg-red-500/20 text-red-400 border-red-500/30' }
    };
    return operationMap[operation as keyof typeof operationMap] || { text: operation, color: 'bg-zinc-500/20 text-zinc-400 border-zinc-500/30' };
  };

  // 格式化时间戳
  const formatTimestamp = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleTimeString('zh-CN', { 
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch {
      return timestamp;
    }
  };

  return (
    <Card className="bg-zinc-900 border-zinc-800">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-white flex items-center gap-2">
            <Activity className="w-5 h-5 text-[#00d4aa]" />
            活动时间线 (多数据源)
            <span className="text-xs text-zinc-500 font-normal ml-2">
              当前数据源: {currentDataSource}
            </span>
          </CardTitle>
          <div className="flex items-center gap-2">
            {/* 数据源切换按钮 */}
            <select 
              value={currentDataSource} 
              onChange={(e) => setCurrentDataSource(e.target.value as DataSource)}
              className="bg-zinc-800 text-zinc-300 border border-zinc-700 rounded px-2 py-1 text-sm"
            >
              <option value="database">数据库API</option>
              <option value="memory_api">记忆API推断</option>
              <option value="local_storage">本地存储</option>
              <option value="mock">模拟数据</option>
            </select>
            
            <Button
              variant="outline"
              size="sm"
              onClick={fetchActivities}
              disabled={loading}
              className="border-zinc-700 text-zinc-300 hover:bg-zinc-800"
            >
              <RefreshCw className={`w-4 h-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
              刷新
            </Button>
          </div>
        </div>
        {error && (
          <div className="text-red-400 text-sm mt-2">
            {error}
          </div>
        )}
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-zinc-800">
                <th className="text-left p-3 text-sm font-medium text-zinc-400">
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4" />
                    时间
                  </div>
                </th>
                <th className="text-left p-3 text-sm font-medium text-zinc-400">
                  <div className="flex items-center gap-2">
                    <Play className="w-4 h-4" />
                    操作
                  </div>
                </th>
                <th className="text-left p-3 text-sm font-medium text-zinc-400">
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4" />
                    用户ID
                  </div>
                </th>
                <th className="text-left p-3 text-sm font-medium text-zinc-400">
                  <div className="flex items-center gap-2">
                    <Database className="w-4 h-4" />
                    元数据
                  </div>
                </th>
                <th className="text-left p-3 text-sm font-medium text-zinc-400">
                  <div className="flex items-center gap-2">
                    <Zap className="w-4 h-4" />
                    响应时间
                  </div>
                </th>
                <th className="text-left p-3 text-sm font-medium text-zinc-400">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4" />
                    状态
                  </div>
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-zinc-800">
              {activities.map((item) => {
                const operationInfo = getOperationInfo(item.operation);
                return (
                  <tr key={item.id} className="hover:bg-zinc-800/30 transition-colors">
                    <td className="p-3 text-sm text-zinc-300 font-mono">
                      {formatTimestamp(item.timestamp)}
                    </td>
                    <td className="p-3">
                      <Badge className={`${operationInfo.color} border text-xs`}>
                        {operationInfo.text}
                      </Badge>
                    </td>
                    <td className="p-3 text-sm text-zinc-300 font-mono">
                      {item.user_id ? (
                        <span className="text-[#00d4aa]">{item.user_id}</span>
                      ) : (
                        <span className="text-zinc-500">-</span>
                      )}
                    </td>
                    <td className="p-3 text-sm text-zinc-300 max-w-xs">
                      <div className="max-w-xs">
                        <CollapsibleMetadata metadata={item.metadata} maxPreviewLength={40} />
                      </div>
                    </td>
                    <td className="p-3 text-sm text-zinc-300 font-mono">
                      <span className={item.response_time ? 'text-[#00d4aa]' : 'text-zinc-500'}>
                        {item.response_time || '-'}
                      </span>
                    </td>
                    <td className="p-3">
                      <Badge className={`${item.status === 'success' ? 'bg-green-500/20 text-green-400 border-green-500/30' : 'bg-red-500/20 text-red-400 border-red-500/30'} border text-xs`}>
                        {item.status}
                      </Badge>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
          
          {activities.length === 0 && !loading && (
            <div className="text-center py-8 text-zinc-500">
              没有找到活动数据
            </div>
          )}
          
          {loading && (
            <div className="text-center py-8 text-zinc-500">
              正在加载活动数据...
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ActivityTimelineAlternative;
