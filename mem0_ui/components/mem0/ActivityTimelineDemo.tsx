'use client';

import React, { useState } from 'react';
import { Clock, Filter, RefreshCw, Activity, ChevronDown, ChevronUp } from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { UIActivityItem } from '@/types/mem0-api';

// 模拟数据 - 更真实的中文场景
const mockActivities: UIActivityItem[] = [
  {
    id: 'act_001',
    timestamp: '2024-01-15T14:30:25.123Z',
    operation: 'SEARCH',
    details: 'User searched for user information',
    response_time: '45ms',
    status: 'success',
    user_id: 'user_12345678',
    memory_id: 'mem_abcd1234',
    request_payload: '{"query": "小明住在哪里？职业是什么？", "user_id": "user_12345678", "limit": 10}',
    action_type: 'SEMANTIC_SEARCH'
  },
  {
    id: 'act_002',
    timestamp: '2024-01-15T14:28:12.456Z',
    operation: 'ADD',
    details: 'Created memory about user preferences',
    response_time: '32ms',
    status: 'success',
    user_id: 'user_12345678',
    memory_id: 'mem_efgh5678',
    request_payload: '{"messages": [{"role": "user", "content": "我喜欢在周末去公园散步，特别是樱花盛开的时候"}], "user_id": "user_12345678"}',
    action_type: 'MEMORY_CREATE'
  },
  {
    id: 'act_003',
    timestamp: '2024-01-15T14:25:03.789Z',
    operation: 'UPDATE',
    details: 'Updated user profile information',
    response_time: '28ms',
    status: 'success',
    user_id: 'user_87654321',
    memory_id: 'mem_ijkl9012',
    request_payload: '{"memory_id": "mem_ijkl9012", "text": "用户现在住在北京朝阳区，从事软件开发工作"}',
    action_type: 'MEMORY_UPDATE'
  },
  {
    id: 'act_004',
    timestamp: '2024-01-15T14:22:45.012Z',
    operation: 'SEARCH',
    details: 'Search for cooking preferences',
    response_time: '38ms',
    status: 'success',
    user_id: 'user_11111111',
    memory_id: 'mem_mnop3456',
    request_payload: '{"query": "用户喜欢什么菜系？有什么饮食偏好？", "user_id": "user_11111111", "filters": {"category": "food"}}',
    action_type: 'SEMANTIC_SEARCH'
  },
  {
    id: 'act_005',
    timestamp: '2024-01-15T14:20:11.345Z',
    operation: 'ADD',
    details: 'Added travel memory',
    response_time: '67ms',
    status: 'success',
    user_id: 'user_22222222',
    memory_id: 'mem_qrst7890',
    request_payload: '{"messages": [{"role": "user", "content": "下个月计划去日本旅行，想去京都和大阪，对传统文化很感兴趣"}], "user_id": "user_22222222", "enable_graph": true}',
    action_type: 'MEMORY_CREATE'
  },
  {
    id: 'act_006',
    timestamp: '2024-01-15T14:18:30.678Z',
    operation: 'SEARCH',
    details: 'Failed search due to empty query',
    response_time: '120ms',
    status: 'error',
    user_id: 'user_33333333',
    request_payload: '{"query": "", "user_id": "user_33333333"}',
    action_type: 'SEMANTIC_SEARCH'
  },
  {
    id: 'act_007',
    timestamp: '2024-01-15T14:15:22.234Z',
    operation: 'DELETE',
    details: 'Deleted outdated memory',
    response_time: '15ms',
    status: 'success',
    user_id: 'user_44444444',
    memory_id: 'mem_uvwx1234',
    request_payload: '{"memory_id": "mem_uvwx1234"}',
    action_type: 'MEMORY_DELETE'
  },
  {
    id: 'act_008',
    timestamp: '2024-01-15T14:12:18.567Z',
    operation: 'SEARCH',
    details: 'Search for work-related memories',
    response_time: '52ms',
    status: 'success',
    user_id: 'user_55555555',
    memory_id: 'mem_yzab5678',
    request_payload: '{"query": "用户的工作经历和技能有哪些？", "user_id": "user_55555555", "limit": 5, "filters": {"category": "work"}}',
    action_type: 'SEMANTIC_SEARCH'
  }
];

const ActivityTimelineDemo: React.FC = () => {
  const [activities] = useState<UIActivityItem[]>(mockActivities);
  const [sortField, setSortField] = useState<'timestamp' | 'operation' | 'user_id' | 'status'>('timestamp');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // 格式化时间戳
  const formatTimestamp = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleTimeString('zh-CN', { 
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch {
      return timestamp;
    }
  };

  // 获取操作类型的颜色和文本
  const getOperationInfo = (operation: string) => {
    const operationMap: Record<string, { color: string; text: string }> = {
      'SEARCH': { color: 'bg-purple-500/20 text-purple-300 border-purple-500/30', text: '搜索' },
      'ADD': { color: 'bg-green-500/20 text-green-300 border-green-500/30', text: '添加' },
      'UPDATE': { color: 'bg-blue-500/20 text-blue-300 border-blue-500/30', text: '更新' },
      'DELETE': { color: 'bg-red-500/20 text-red-300 border-red-500/30', text: '删除' },
      'GRAPH_CREATE': { color: 'bg-cyan-500/20 text-cyan-300 border-cyan-500/30', text: '图创建' }
    };
    return operationMap[operation.toUpperCase()] || { color: 'bg-zinc-500/20 text-zinc-300 border-zinc-500/30', text: operation };
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'success':
        return 'bg-green-500/20 text-green-300 border-green-500/30';
      case 'error':
        return 'bg-red-500/20 text-red-300 border-red-500/30';
      case 'pending':
        return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30';
      default:
        return 'bg-zinc-500/20 text-zinc-300 border-zinc-500/30';
    }
  };

  // 格式化和显示请求负载
  const formatRequestPayload = (payload: string | undefined) => {
    if (!payload) return null;

    try {
      const parsed = JSON.parse(payload);

      // 提取关键信息进行简化显示
      if (parsed.query) {
        return (
          <div className="space-y-1">
            <div className="text-zinc-300 text-xs">
              <span className="text-zinc-500">query:</span> "{parsed.query}"
            </div>
            {parsed.user_id && (
              <div className="text-zinc-400 text-xs">
                <span className="text-zinc-500">user:</span> {parsed.user_id.slice(-8)}
              </div>
            )}
          </div>
        );
      }

      if (parsed.messages && parsed.messages[0]) {
        const content = parsed.messages[0].content;
        return (
          <div className="space-y-1">
            <div className="text-zinc-300 text-xs">
              <span className="text-zinc-500">content:</span> "{content.length > 30 ? content.substring(0, 30) + '...' : content}"
            </div>
            {parsed.user_id && (
              <div className="text-zinc-400 text-xs">
                <span className="text-zinc-500">user:</span> {parsed.user_id.slice(-8)}
              </div>
            )}
          </div>
        );
      }

      if (parsed.memory_id) {
        return (
          <div className="space-y-1">
            <div className="text-zinc-300 text-xs">
              <span className="text-zinc-500">memory:</span> #{parsed.memory_id.slice(-8)}
            </div>
            {parsed.text && (
              <div className="text-zinc-400 text-xs">
                <span className="text-zinc-500">text:</span> "{parsed.text.length > 25 ? parsed.text.substring(0, 25) + '...' : parsed.text}"
              </div>
            )}
          </div>
        );
      }

      // 默认显示原始JSON（截断）
      const jsonStr = JSON.stringify(parsed, null, 0);
      return (
        <code className="text-xs text-zinc-400 bg-zinc-800/50 px-2 py-1 rounded">
          {jsonStr.length > 40 ? jsonStr.substring(0, 40) + '...' : jsonStr}
        </code>
      );
    } catch {
      // 如果不是有效JSON，直接显示原始文本
      return (
        <span className="text-zinc-400 text-xs">
          {payload.length > 40 ? payload.substring(0, 40) + '...' : payload}
        </span>
      );
    }
  };

  // 排序功能
  const handleSort = (field: typeof sortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  // 排序活动数据
  const sortedActivities = [...activities].sort((a, b) => {
    let aValue: any = a[sortField];
    let bValue: any = b[sortField];

    if (sortField === 'timestamp') {
      aValue = new Date(aValue).getTime();
      bValue = new Date(bValue).getTime();
    }

    if (sortDirection === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  return (
    <Card className="bg-zinc-900 border-zinc-800">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-white flex items-center gap-2">
            <Activity className="w-5 h-5 text-[#00d4aa]" />
            活动时间线 - 表格视图演示
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              className="border-zinc-700 hover:border-zinc-600"
            >
              <Filter className="w-4 h-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="border-zinc-700 hover:border-zinc-600"
            >
              <RefreshCw className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-zinc-800">
                <th 
                  className="text-left p-3 text-xs font-medium text-zinc-400 uppercase tracking-wider cursor-pointer hover:text-zinc-300 transition-colors"
                  onClick={() => handleSort('timestamp')}
                >
                  <div className="flex items-center gap-1">
                    Time
                    {sortField === 'timestamp' && (
                      sortDirection === 'desc' ? <ChevronDown className="w-3 h-3" /> : <ChevronUp className="w-3 h-3" />
                    )}
                  </div>
                </th>
                <th 
                  className="text-left p-3 text-xs font-medium text-zinc-400 uppercase tracking-wider cursor-pointer hover:text-zinc-300 transition-colors"
                  onClick={() => handleSort('operation')}
                >
                  <div className="flex items-center gap-1">
                    Event Type
                    {sortField === 'operation' && (
                      sortDirection === 'desc' ? <ChevronDown className="w-3 h-3" /> : <ChevronUp className="w-3 h-3" />
                    )}
                  </div>
                </th>
                <th 
                  className="text-left p-3 text-xs font-medium text-zinc-400 uppercase tracking-wider cursor-pointer hover:text-zinc-300 transition-colors"
                  onClick={() => handleSort('user_id')}
                >
                  <div className="flex items-center gap-1">
                    User ID
                    {sortField === 'user_id' && (
                      sortDirection === 'desc' ? <ChevronDown className="w-3 h-3" /> : <ChevronUp className="w-3 h-3" />
                    )}
                  </div>
                </th>
                <th className="text-left p-3 text-xs font-medium text-zinc-400 uppercase tracking-wider">
                  Request Payload
                </th>
                <th className="text-left p-3 text-xs font-medium text-zinc-400 uppercase tracking-wider">
                  Latency
                </th>
                <th 
                  className="text-left p-3 text-xs font-medium text-zinc-400 uppercase tracking-wider cursor-pointer hover:text-zinc-300 transition-colors"
                  onClick={() => handleSort('status')}
                >
                  <div className="flex items-center gap-1">
                    Status
                    {sortField === 'status' && (
                      sortDirection === 'desc' ? <ChevronDown className="w-3 h-3" /> : <ChevronUp className="w-3 h-3" />
                    )}
                  </div>
                </th>
                <th className="text-left p-3 text-xs font-medium text-zinc-400 uppercase tracking-wider">
                  Action Type
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-zinc-800">
              {sortedActivities.map((item) => {
                const operationInfo = getOperationInfo(item.operation);
                return (
                  <tr key={item.id} className="hover:bg-zinc-800/30 transition-colors">
                    <td className="p-3 text-sm text-zinc-300 font-mono">
                      {formatTimestamp(item.timestamp)}
                    </td>
                    <td className="p-3">
                      <Badge className={`${operationInfo.color} border text-xs`}>
                        {operationInfo.text}
                      </Badge>
                    </td>
                    <td className="p-3 text-sm text-zinc-300 font-mono">
                      {item.user_id ? (
                        <span className="text-[#00d4aa]">{item.user_id.slice(-8)}</span>
                      ) : (
                        <span className="text-zinc-500">-</span>
                      )}
                    </td>
                    <td className="p-3 text-sm text-zinc-300 max-w-xs">
                      <div className="max-w-xs" title={item.request_payload || item.details}>
                        {item.request_payload ? (
                          formatRequestPayload(item.request_payload)
                        ) : (
                          <span className="text-zinc-400 text-xs">{item.details || '-'}</span>
                        )}
                      </div>
                    </td>
                    <td className="p-3 text-sm text-zinc-300 font-mono">
                      <span className={item.response_time ? 'text-[#00d4aa]' : 'text-zinc-500'}>
                        {item.response_time || '-'}
                      </span>
                    </td>
                    <td className="p-3">
                      <Badge className={`${getStatusColor(item.status)} border text-xs`}>
                        {item.status}
                      </Badge>
                    </td>
                    <td className="p-3 text-sm text-zinc-300">
                      {item.action_type ? (
                        <span className="text-zinc-300 bg-zinc-800 px-2 py-1 rounded text-xs font-medium">
                          {item.action_type}
                        </span>
                      ) : item.memory_id ? (
                        <span className="text-zinc-400 font-mono text-xs">#{item.memory_id.slice(-8)}</span>
                      ) : (
                        <span className="text-zinc-500">-</span>
                      )}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
};

export default ActivityTimelineDemo;
