'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Activity, Wifi, WifiOff, Play, Pause } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import CollapsibleMetadata from './CollapsibleMetadata';

interface RealTimeActivity {
  id: string;
  timestamp: string;
  operation: string;
  endpoint: string;
  method: string;
  status: number;
  responseTime: number;
  requestData?: any;
  responseData?: any;
  userAgent?: string;
}

interface RealTimeActivityMonitorProps {
  maxActivities?: number;
  autoScroll?: boolean;
}

const RealTimeActivityMonitor: React.FC<RealTimeActivityMonitorProps> = ({
  maxActivities = 50,
  autoScroll = true
}) => {
  const [activities, setActivities] = useState<RealTimeActivity[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastCheckRef = useRef<string>(new Date().toISOString());

  // 模拟实时监控 - 在实际应用中，这里应该是WebSocket或Server-Sent Events
  const startMonitoring = () => {
    if (intervalRef.current) return;

    setIsMonitoring(true);
    setIsConnected(true);

    // 每2秒检查一次新的活动
    intervalRef.current = setInterval(async () => {
      try {
        // 方法1: 轮询活动API
        await pollActivities();
        
        // 方法2: 监控网络请求 (在实际应用中可以使用Service Worker)
        // monitorNetworkRequests();
        
      } catch (error) {
        console.error('监控活动时出错:', error);
        setIsConnected(false);
      }
    }, 2000);
  };

  const stopMonitoring = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    setIsMonitoring(false);
    setIsConnected(false);
  };

  // 轮询活动数据
  const pollActivities = async () => {
    try {
      const response = await fetch(`http://localhost:8000/v1/activities/?limit=10&start_time=${lastCheckRef.current}`);
      if (response.ok) {
        const data = await response.json();
        const newActivities = data.activities || [];
        
        if (newActivities.length > 0) {
          // 转换为实时活动格式
          const realTimeActivities: RealTimeActivity[] = newActivities.map((activity: any) => ({
            id: activity.id,
            timestamp: activity.timestamp,
            operation: activity.operation,
            endpoint: getEndpointFromOperation(activity.operation),
            method: getMethodFromOperation(activity.operation),
            status: 200,
            responseTime: parseResponseTime(activity.response_time),
            requestData: {
              user_id: activity.user_id,
              memory_id: activity.memory_id
            },
            responseData: activity.metadata,
            userAgent: 'Mem0 Client'
          }));

          setActivities(prev => {
            const combined = [...realTimeActivities, ...prev];
            return combined.slice(0, maxActivities);
          });

          // 更新最后检查时间
          lastCheckRef.current = new Date().toISOString();
        }
      }
    } catch (error) {
      console.error('轮询活动失败:', error);
      setIsConnected(false);
    }
  };

  // 根据操作类型推断端点
  const getEndpointFromOperation = (operation: string): string => {
    const endpointMap: Record<string, string> = {
      'ADD': '/v1/memories/',
      'SEARCH': '/v1/memories/search',
      'UPDATE': '/v1/memories/{id}',
      'DELETE': '/v1/memories/{id}'
    };
    return endpointMap[operation] || '/v1/unknown';
  };

  // 根据操作类型推断HTTP方法
  const getMethodFromOperation = (operation: string): string => {
    const methodMap: Record<string, string> = {
      'ADD': 'POST',
      'SEARCH': 'POST',
      'UPDATE': 'PUT',
      'DELETE': 'DELETE'
    };
    return methodMap[operation] || 'GET';
  };

  // 解析响应时间
  const parseResponseTime = (responseTime?: string): number => {
    if (!responseTime) return 0;
    const match = responseTime.match(/(\d+)/);
    return match ? parseInt(match[1]) : 0;
  };

  // 获取状态颜色
  const getStatusColor = (status: number): string => {
    if (status >= 200 && status < 300) return 'text-green-400';
    if (status >= 400 && status < 500) return 'text-yellow-400';
    if (status >= 500) return 'text-red-400';
    return 'text-zinc-400';
  };

  // 获取方法颜色
  const getMethodColor = (method: string): string => {
    const colorMap: Record<string, string> = {
      'GET': 'bg-blue-500/20 text-blue-400 border-blue-500/30',
      'POST': 'bg-green-500/20 text-green-400 border-green-500/30',
      'PUT': 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30',
      'DELETE': 'bg-red-500/20 text-red-400 border-red-500/30'
    };
    return colorMap[method] || 'bg-zinc-500/20 text-zinc-400 border-zinc-500/30';
  };

  // 格式化时间戳
  const formatTimestamp = (timestamp: string): string => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleTimeString('zh-CN', { 
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        fractionalSecondDigits: 3
      });
    } catch {
      return timestamp;
    }
  };

  // 添加模拟活动 (用于演示)
  const addMockActivity = () => {
    const operations = ['ADD', 'SEARCH', 'UPDATE', 'DELETE'];
    const operation = operations[Math.floor(Math.random() * operations.length)];
    
    const mockActivity: RealTimeActivity = {
      id: `mock_${Date.now()}`,
      timestamp: new Date().toISOString(),
      operation,
      endpoint: getEndpointFromOperation(operation),
      method: getMethodFromOperation(operation),
      status: Math.random() > 0.1 ? 200 : (Math.random() > 0.5 ? 400 : 500),
      responseTime: Math.floor(Math.random() * 500) + 50,
      requestData: {
        user_id: `user_${Math.floor(Math.random() * 100)}`,
        memory_id: `memory_${Math.floor(Math.random() * 1000)}`
      },
      responseData: {
        operation_type: operation.toLowerCase(),
        timestamp: new Date().toISOString(),
        success: Math.random() > 0.1
      },
      userAgent: 'Mock Client'
    };

    setActivities(prev => [mockActivity, ...prev.slice(0, maxActivities - 1)]);
  };

  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return (
    <Card className="bg-zinc-900 border-zinc-800">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-white flex items-center gap-2">
            <Activity className="w-5 h-5 text-[#00d4aa]" />
            实时活动监控
            <div className="flex items-center gap-2 ml-4">
              {isConnected ? (
                <Wifi className="w-4 h-4 text-green-400" />
              ) : (
                <WifiOff className="w-4 h-4 text-red-400" />
              )}
              <span className="text-xs text-zinc-500">
                {isConnected ? '已连接' : '未连接'}
              </span>
            </div>
          </CardTitle>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={addMockActivity}
              className="border-zinc-700 text-zinc-300 hover:bg-zinc-800"
            >
              添加模拟活动
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={isMonitoring ? stopMonitoring : startMonitoring}
              className="border-zinc-700 text-zinc-300 hover:bg-zinc-800"
            >
              {isMonitoring ? (
                <>
                  <Pause className="w-4 h-4 mr-1" />
                  停止监控
                </>
              ) : (
                <>
                  <Play className="w-4 h-4 mr-1" />
                  开始监控
                </>
              )}
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {activities.map((activity) => (
            <div
              key={activity.id}
              className="p-3 bg-zinc-800/50 border border-zinc-700 rounded-lg hover:bg-zinc-800/70 transition-colors"
            >
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-3">
                  <span className="text-xs text-zinc-400 font-mono">
                    {formatTimestamp(activity.timestamp)}
                  </span>
                  
                  <Badge className={`${getMethodColor(activity.method)} border text-xs`}>
                    {activity.method}
                  </Badge>
                  
                  <span className="text-sm text-zinc-300 font-mono">
                    {activity.endpoint}
                  </span>
                </div>
                
                <div className="flex items-center gap-3">
                  <span className={`text-sm font-mono ${getStatusColor(activity.status)}`}>
                    {activity.status}
                  </span>
                  
                  <span className="text-xs text-zinc-400">
                    {activity.responseTime}ms
                  </span>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-xs">
                <div>
                  <span className="text-zinc-500">请求数据:</span>
                  <div className="mt-1">
                    <CollapsibleMetadata 
                      metadata={activity.requestData} 
                      maxPreviewLength={30} 
                    />
                  </div>
                </div>
                
                <div>
                  <span className="text-zinc-500">响应数据:</span>
                  <div className="mt-1">
                    <CollapsibleMetadata 
                      metadata={activity.responseData} 
                      maxPreviewLength={30} 
                    />
                  </div>
                </div>
              </div>
            </div>
          ))}
          
          {activities.length === 0 && (
            <div className="text-center py-8 text-zinc-500">
              暂无实时活动数据
              {!isMonitoring && (
                <div className="mt-2 text-xs">
                  点击"开始监控"开始监控实时活动
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default RealTimeActivityMonitor;
