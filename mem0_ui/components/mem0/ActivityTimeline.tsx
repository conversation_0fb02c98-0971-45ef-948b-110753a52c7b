import React, { useState, useEffect, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { Clock, Filter, RefreshCw, Activity, ChevronDown, ChevronUp, User, Database, Zap, CheckCircle, Play, Eye, EyeOff } from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { realMem0Client } from '@/lib/mem0-client/realClient';
import { UIActivityItem, UIActivitiesResponse } from '@/types/mem0-api';
import { RootState } from '@/store/store';

import CategoryFilter from './CategoryFilter';
import CollapsibleMetadata from './CollapsibleMetadata';

interface ActivityTimelineProps {
  filterUserId?: string;
  filterRunId?: string;
  filterAgentId?: string;
  filterCategories?: string[];
  onFiltersChange?: (filters: {
    userId?: string;
    runId?: string;
    agentId?: string;
    categories?: string[];
  }) => void;
  className?: string;
}

const ActivityTimeline: React.FC<ActivityTimelineProps> = ({
  filterUserId,
  filterRunId,
  filterAgentId,
  filterCategories = [],
  onFiltersChange,
  className
}) => {
  const [activities, setActivities] = useState<UIActivityItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [categories, setCategories] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const [sortField, setSortField] = useState<'timestamp' | 'operation' | 'user_id' | 'status'>('timestamp');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  
  // 分页状态
  const [currentOffset, setCurrentOffset] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const ITEMS_PER_PAGE = 10;

  // 简化的过滤选项 - 默认显示所有数据
  const [hideUIOperations, setHideUIOperations] = useState(false);
  const [hideEmptyMetadata, setHideEmptyMetadata] = useState(false);
  const [showSearchOperations, setShowSearchOperations] = useState(true);
  const [hideBasicCRUD, setHideBasicCRUD] = useState(false);

  const { toast } = useToast();
  const userId = useSelector((state: RootState) => state.profile.userId);

  // 格式化时间戳
  const formatTimestamp = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch {
      return timestamp;
    }
  };

  // 获取操作类型的颜色和文本
  const getOperationInfo = (operation: string) => {
    const operationMap: Record<string, { color: string; text: string }> = {
      'SEARCH': { color: 'bg-purple-500/20 text-purple-300 border-purple-500/30', text: '搜索' },
      'ADD': { color: 'bg-green-500/20 text-green-300 border-green-500/30', text: '添加' },
      'UPDATE': { color: 'bg-blue-500/20 text-blue-300 border-blue-500/30', text: '更新' },
      'DELETE': { color: 'bg-red-500/20 text-red-300 border-red-500/30', text: '删除' },
      'GRAPH_CREATE': { color: 'bg-cyan-500/20 text-cyan-300 border-cyan-500/30', text: '图创建' }
    };
    return operationMap[operation.toUpperCase()] || { color: 'bg-zinc-500/20 text-zinc-300 border-zinc-500/30', text: operation };
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'success':
        return 'bg-green-500/20 text-green-300 border-green-500/30';
      case 'error':
        return 'bg-red-500/20 text-red-300 border-red-500/30';
      case 'pending':
        return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30';
      default:
        return 'bg-zinc-500/20 text-zinc-300 border-zinc-500/30';
    }
  };

  // 排序功能
  const handleSort = (field: typeof sortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  // 排序活动数据
  const sortedActivities = [...activities].sort((a, b) => {
    let aValue: any = a[sortField];
    let bValue: any = b[sortField];

    if (sortField === 'timestamp') {
      aValue = new Date(aValue).getTime();
      bValue = new Date(bValue).getTime();
    }

    if (sortDirection === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  const fetchActivities = useCallback(async (offset: number = 0, appendMode: boolean = false) => {
    console.log('🔄 fetchActivities 被调用，分页参数:', {
      offset,
      appendMode,
      limit: ITEMS_PER_PAGE,
      过滤器状态: {
        hideUIOperations,
        hideEmptyMetadata,
        showSearchOperations,
        hideBasicCRUD
      }
    });
    
    if (!appendMode) {
      setLoading(true);
      setActivities([]);
      setCurrentOffset(0);
    } else {
      setLoadingMore(true);
    }
    setError(null);

    try {
      // 使用正确的UI活动API端点
      const queryParams: any = {
        limit: ITEMS_PER_PAGE,
        offset: offset
      };
      if (filterUserId) queryParams.user_id = filterUserId;

      // 构建查询参数
      const urlParams = new URLSearchParams(queryParams);
      const apiUrl = `http://localhost:8000/v1/activities/?${urlParams.toString()}`;
      console.log('API请求URL:', apiUrl);

      // 直接使用fetch调用，绕过可能有问题的realMem0Client
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const activitiesResponse: UIActivitiesResponse = await response.json();

      // 调试：打印接收到的数据
      console.log('ActivityTimeline - 接收到的活动数据:', {
        总数: activitiesResponse.total,
        当前批次数量: activitiesResponse.activities.length,
        hasMore: activitiesResponse.has_more,
        offset: offset
      });

      // 更新状态
      setTotalCount(activitiesResponse.total || 0);
      setHasMore(activitiesResponse.has_more || false);
      
      if (appendMode) {
        // 追加模式：添加到现有数据
        setActivities(prevActivities => [...prevActivities, ...activitiesResponse.activities]);
        setCurrentOffset(offset + ITEMS_PER_PAGE);
      } else {
        // 刷新模式：替换所有数据
        setActivities(activitiesResponse.activities);
        setCurrentOffset(ITEMS_PER_PAGE);
      }

      setLastRefresh(new Date());
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch activity timeline';
      setError(errorMessage);
      console.error('Failed to fetch activities:', err);
      toast({
        title: "错误",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  }, [filterUserId, toast, ITEMS_PER_PAGE]);

  // 加载更多数据的函数
  const loadMoreActivities = useCallback(() => {
    if (!loadingMore && hasMore) {
      fetchActivities(currentOffset, true);
    }
  }, [fetchActivities, currentOffset, loadingMore, hasMore]);

  const fetchAvailableCategories = useCallback(async () => {
    try {
      const memoriesResponse = await realMem0Client.getMemories({ 
        user_id: userId, 
        limit: 1000 
      });
      const allCategories = memoriesResponse.memories
        .flatMap(memory => memory.custom_categories || [])
        .filter(Boolean);
      
      const uniqueCategories = [...new Set(allCategories)];
      setCategories(uniqueCategories);
    } catch (err) {
      console.error('Failed to fetch categories:', err);
    }
  }, []);

  const handleRefresh = useCallback(() => {
    setCurrentOffset(0);
    setHasMore(true);
    fetchActivities(0, false);
    fetchAvailableCategories();
  }, [fetchActivities, fetchAvailableCategories]);

  const handleCategoryFilterChange = useCallback((selectedCategories: string[]) => {
    // 构建过滤器参数，过滤掉undefined值
    const filterParams: any = { categories: selectedCategories };
    if (filterUserId) filterParams.userId = filterUserId;
    if (filterRunId) filterParams.runId = filterRunId;
    if (filterAgentId) filterParams.agentId = filterAgentId;

    onFiltersChange?.(filterParams);
  }, [filterUserId, filterRunId, filterAgentId, onFiltersChange]);

  // 初始化数据
  useEffect(() => {
    fetchActivities(0, false);
    fetchAvailableCategories();
  }, [fetchActivities, fetchAvailableCategories]);

  // 自动刷新（每30秒）
  useEffect(() => {
    const interval = setInterval(() => {
      handleRefresh();
    }, 30000);

    return () => clearInterval(interval);
  }, [handleRefresh]);

  if (error) {
    return (
      <Card className={`bg-zinc-900 border-zinc-800 ${className}`}>
        <CardContent className="p-6">
          <div className="text-center text-red-400">
            <Activity className="w-8 h-8 mx-auto mb-2" />
            <p>加载活动时间线失败</p>
            <p className="text-sm text-zinc-500 mt-1">{error}</p>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleRefresh}
              className="mt-4 border-zinc-700"
            >
              重试
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`bg-zinc-900 border-zinc-800 ${className}`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-white flex items-center gap-2">
            <Activity className="w-5 h-5 text-[#00d4aa]" />
            活动时间线
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className="border-zinc-700 hover:border-zinc-600"
            >
              <Filter className="w-4 h-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={loading}
              className="border-zinc-700 hover:border-zinc-600"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
        
        {showFilters && (
          <div className="mt-4 space-y-4">
            {/* 简化的过滤控制 */}
            <div className="flex flex-wrap gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setHideUIOperations(false);
                  setHideEmptyMetadata(false);
                  setShowSearchOperations(true);
                  setHideBasicCRUD(false);
                }}
                className="border-zinc-700 text-zinc-300 hover:bg-zinc-800"
              >
                重置过滤器
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  console.log('🧪 手动测试过滤器状态:', {
                    hideUIOperations,
                    hideEmptyMetadata,
                    showSearchOperations,
                    hideBasicCRUD,
                    当前显示记录数: activities.length
                  });
                  // 强制刷新数据来触发过滤
                  fetchActivities();
                }}
                className="border-zinc-700 text-zinc-300 hover:bg-zinc-800"
              >
                🧪 测试过滤器
              </Button>
              <Button
                variant={hideUIOperations ? "default" : "outline"}
                size="sm"
                onClick={() => setHideUIOperations(!hideUIOperations)}
                className={hideUIOperations
                  ? "bg-[#00d4aa] text-black hover:bg-[#00b89a]"
                  : "border-zinc-700 text-zinc-300 hover:bg-zinc-800"
                }
              >
                {hideUIOperations ? <EyeOff className="w-4 h-4 mr-1" /> : <Eye className="w-4 h-4 mr-1" />}
                隐藏UI操作
              </Button>

              <Button
                variant={hideEmptyMetadata ? "default" : "outline"}
                size="sm"
                onClick={() => {
                  console.log('🔘 点击隐藏空数据按钮，当前状态:', hideEmptyMetadata, '-> 新状态:', !hideEmptyMetadata);
                  setHideEmptyMetadata(!hideEmptyMetadata);
                }}
                className={hideEmptyMetadata
                  ? "bg-[#00d4aa] text-black hover:bg-[#00b89a]"
                  : "border-zinc-700 text-zinc-300 hover:bg-zinc-800"
                }
              >
                {hideEmptyMetadata ? <EyeOff className="w-4 h-4 mr-1" /> : <Eye className="w-4 h-4 mr-1" />}
                隐藏空数据
              </Button>

              <Button
                variant={showSearchOperations ? "default" : "outline"}
                size="sm"
                onClick={() => setShowSearchOperations(!showSearchOperations)}
                className={showSearchOperations
                  ? "bg-[#00d4aa] text-black hover:bg-[#00b89a]"
                  : "border-zinc-700 text-zinc-300 hover:bg-zinc-800"
                }
              >
                {showSearchOperations ? <Eye className="w-4 h-4 mr-1" /> : <EyeOff className="w-4 h-4 mr-1" />}
                显示搜索操作
              </Button>

              <Button
                variant={hideBasicCRUD ? "default" : "outline"}
                size="sm"
                onClick={() => setHideBasicCRUD(!hideBasicCRUD)}
                className={hideBasicCRUD
                  ? "bg-[#00d4aa] text-black hover:bg-[#00b89a]"
                  : "border-zinc-700 text-zinc-300 hover:bg-zinc-800"
                }
              >
                {hideBasicCRUD ? <EyeOff className="w-4 h-4 mr-1" /> : <Eye className="w-4 h-4 mr-1" />}
                隐藏基本操作
              </Button>
            </div>

            <CategoryFilter
              categories={categories}
              selectedCategories={filterCategories}
              onCategoryChange={handleCategoryFilterChange}
            />
          </div>
        )}
        
        <div className="flex items-center gap-2 text-xs text-zinc-500">
          <Clock className="w-3 h-3" />
          <span>最后更新: {lastRefresh.toLocaleTimeString()}</span>
          <span>•</span>
          <span>{activities.length} / {totalCount} 条活动记录{hasMore ? ' (部分显示)' : ''}</span>
          {(hideUIOperations || hideEmptyMetadata || !showSearchOperations || hideBasicCRUD) && (
            <>
              <span>•</span>
              <span className="text-[#00d4aa]">已应用过滤器</span>
            </>
          )}
          {activities.length === 0 && (
            <>
              <span>•</span>
              <span className="text-red-400">无数据显示，请检查过滤设置</span>
            </>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="p-0">
        {loading ? (
          <div className="p-6">
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex items-start gap-4">
                  <div className="w-8 h-8 bg-zinc-800 rounded-full animate-pulse"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-zinc-800 rounded animate-pulse"></div>
                    <div className="h-3 bg-zinc-800 rounded w-3/4 animate-pulse"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : activities.length === 0 ? (
          <div className="p-6 text-center text-zinc-500">
            <Activity className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p>暂无活动记录</p>
            <p className="text-sm mt-1">当有记忆操作时，活动记录将显示在这里</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-zinc-800">
                  <th
                    className="text-left p-3 text-xs font-medium text-zinc-400 uppercase tracking-wider cursor-pointer hover:text-zinc-300 transition-colors"
                    onClick={() => handleSort('timestamp')}
                  >
                    <div className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      Time
                      {sortField === 'timestamp' && (
                        sortDirection === 'desc' ? <ChevronDown className="w-3 h-3" /> : <ChevronUp className="w-3 h-3" />
                      )}
                    </div>
                  </th>
                  <th
                    className="text-left p-3 text-xs font-medium text-zinc-400 uppercase tracking-wider cursor-pointer hover:text-zinc-300 transition-colors"
                    onClick={() => handleSort('operation')}
                  >
                    <div className="flex items-center gap-1">
                      <Play className="w-3 h-3" />
                      Event Type
                      {sortField === 'operation' && (
                        sortDirection === 'desc' ? <ChevronDown className="w-3 h-3" /> : <ChevronUp className="w-3 h-3" />
                      )}
                    </div>
                  </th>
                  <th
                    className="text-left p-3 text-xs font-medium text-zinc-400 uppercase tracking-wider cursor-pointer hover:text-zinc-300 transition-colors"
                    onClick={() => handleSort('user_id')}
                  >
                    <div className="flex items-center gap-1">
                      <User className="w-3 h-3" />
                      User ID
                      {sortField === 'user_id' && (
                        sortDirection === 'desc' ? <ChevronDown className="w-3 h-3" /> : <ChevronUp className="w-3 h-3" />
                      )}
                    </div>
                  </th>
                  <th className="text-left p-3 text-xs font-medium text-zinc-400 uppercase tracking-wider">
                    <div className="flex items-center gap-1">
                      <Database className="w-3 h-3" />
                      Metadata
                    </div>
                  </th>
                  <th className="text-left p-3 text-xs font-medium text-zinc-400 uppercase tracking-wider">
                    <div className="flex items-center gap-1">
                      <Zap className="w-3 h-3" />
                      Latency
                    </div>
                  </th>
                  <th
                    className="text-left p-3 text-xs font-medium text-zinc-400 uppercase tracking-wider cursor-pointer hover:text-zinc-300 transition-colors"
                    onClick={() => handleSort('status')}
                  >
                    <div className="flex items-center gap-1">
                      <CheckCircle className="w-3 h-3" />
                      Status
                      {sortField === 'status' && (
                        sortDirection === 'desc' ? <ChevronDown className="w-3 h-3" /> : <ChevronUp className="w-3 h-3" />
                      )}
                    </div>
                  </th>

                </tr>
              </thead>
              <tbody className="divide-y divide-zinc-800">
                {sortedActivities.map((item) => {
                  const operationInfo = getOperationInfo(item.operation);
                  return (
                    <tr key={item.id} className="hover:bg-zinc-800/30 transition-colors">
                      <td className="p-3 text-sm text-zinc-300 font-mono">
                        {formatTimestamp(item.timestamp)}
                      </td>
                      <td className="p-3">
                        <Badge className={`${operationInfo.color} border text-xs`}>
                          {operationInfo.text}
                        </Badge>
                      </td>
                      <td className="p-3 text-sm text-zinc-300 font-mono">
                        {item.user_id ? (
                          <span className="text-[#00d4aa]">{item.user_id}</span>
                        ) : (
                          <span className="text-zinc-500">-</span>
                        )}
                      </td>
                      <td className="p-3 text-sm text-zinc-300 max-w-xs">
                        <div className="max-w-xs">
                          <CollapsibleMetadata metadata={item.metadata} maxPreviewLength={40} />
                        </div>
                      </td>
                      <td className="p-3 text-sm text-zinc-300 font-mono">
                        <span className={item.response_time ? 'text-[#00d4aa]' : 'text-zinc-500'}>
                          {item.response_time || '-'}
                        </span>
                      </td>
                      <td className="p-3">
                        <Badge className={`${getStatusColor(item.status)} border text-xs`}>
                          {item.status}
                        </Badge>
                      </td>

                    </tr>
                  );
                })}
              </tbody>
            </table>

            {/* 分页信息和加载更多按钮 */}
            <div className="mt-4 flex items-center justify-between border-t border-zinc-800 pt-4">
              <div className="text-sm text-zinc-500">
                显示 {activities.length} / {totalCount} 条记录
                {hasMore && ` (还有更多数据)`}
              </div>
              
              {hasMore && (
                <Button
                  variant="outline"
                  onClick={loadMoreActivities}
                  disabled={loadingMore}
                  className="border-zinc-700 text-zinc-300 hover:bg-zinc-800 disabled:opacity-50"
                >
                  {loadingMore ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      加载中...
                    </>
                  ) : (
                    <>
                      <ChevronDown className="w-4 h-4 mr-2" />
                      加载更多
                    </>
                  )}
                </Button>
              )}
            </div>

            {sortedActivities.length === 0 && (
              <div className="text-center py-12">
                <div className="text-zinc-500 mb-4">
                  <Activity className="w-12 h-12 mx-auto mb-2 opacity-50" />
                  <p className="text-lg">没有找到活动数据</p>
                  <p className="text-sm mt-2">
                    {(hideUIOperations || hideEmptyMetadata || !showSearchOperations || hideBasicCRUD)
                      ? "当前过滤器可能过于严格，请尝试调整过滤设置"
                      : "系统中暂无活动记录"
                    }
                  </p>
                </div>
                {(hideUIOperations || hideEmptyMetadata || !showSearchOperations || hideBasicCRUD) && (
                  <Button
                    variant="outline"
                    onClick={() => {
                      setHideUIOperations(false);
                      setHideEmptyMetadata(false);
                      setShowSearchOperations(true);
                      setHideBasicCRUD(false);
                    }}
                    className="border-zinc-700 text-zinc-300 hover:bg-zinc-800"
                  >
                    重置所有过滤器
                  </Button>
                )}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ActivityTimeline;
