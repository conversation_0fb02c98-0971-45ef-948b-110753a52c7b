'use client';

import React, { useState, useEffect } from 'react';
import { Clock, Filter, RefreshCw, Activity, ChevronDown, ChevronUp, Loader2, User, Database, Zap, CheckCircle, Play } from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { UIActivityItem } from '@/types/mem0-api';
import CollapsibleMetadata from './CollapsibleMetadata';

// API调用函数
const fetchActivities = async (limit: number = 50): Promise<UIActivityItem[]> => {
  try {
    const response = await fetch(`http://localhost:8000/v1/activities/?limit=${limit}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      }
    });
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    console.log('ActivityTimelineReal - API响应:', data);

    // 处理API响应数据
    const activities = data.activities || data || [];

    // 调试：检查metadata情况
    console.log('ActivityTimelineReal - 前3条记录的metadata:',
      activities.slice(0, 3).map((a: any) => ({
        id: a.id,
        user_id: a.user_id,
        metadata: a.metadata,
        metadata_type: typeof a.metadata
      }))
    );

    // 直接返回真实API数据，不添加任何模拟元数据
    return activities;
  } catch (error) {
    console.error('Failed to fetch activities:', error);
    throw error; // 让调用者处理错误
  }
};





const ActivityTimelineReal: React.FC = () => {
  const [activities, setActivities] = useState<UIActivityItem[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [sortField, setSortField] = useState<'timestamp' | 'operation' | 'user_id' | 'status'>('timestamp');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // 加载活动数据
  const loadActivities = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await fetchActivities();
      setActivities(data);
      console.log('Loaded activities:', data.length, 'items');
    } catch (err) {
      setError('Failed to load activities');
      console.error('Error loading activities:', err);
      // 如果API失败，设置空数组
      setActivities([]);
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时加载数据
  useEffect(() => {
    loadActivities();
  }, []);

  // 格式化时间戳
  const formatTimestamp = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleTimeString('zh-CN', { 
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch {
      return timestamp;
    }
  };

  // 获取操作类型的颜色和文本
  const getOperationInfo = (operation: string) => {
    const operationMap: Record<string, { color: string; text: string }> = {
      'SEARCH': { color: 'bg-purple-500/20 text-purple-300 border-purple-500/30', text: '搜索' },
      'ADD': { color: 'bg-green-500/20 text-green-300 border-green-500/30', text: '添加' },
      'UPDATE': { color: 'bg-blue-500/20 text-blue-300 border-blue-500/30', text: '更新' },
      'DELETE': { color: 'bg-red-500/20 text-red-300 border-red-500/30', text: '删除' },
      'GRAPH_CREATE': { color: 'bg-cyan-500/20 text-cyan-300 border-cyan-500/30', text: '图创建' }
    };
    return operationMap[operation.toUpperCase()] || { color: 'bg-zinc-500/20 text-zinc-300 border-zinc-500/30', text: operation };
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'success':
        return 'bg-green-500/20 text-green-300 border-green-500/30';
      case 'error':
        return 'bg-red-500/20 text-red-300 border-red-500/30';
      case 'pending':
        return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30';
      default:
        return 'bg-zinc-500/20 text-zinc-300 border-zinc-500/30';
    }
  };

  // 格式化和显示元数据 - 基于真实API数据结构
  const formatMetadata = (item: UIActivityItem) => {
    const { metadata } = item;

    // 如果有真实的metadata，优先显示
    if (metadata && Object.keys(metadata).length > 0) {
      // 提取关键信息进行显示
      if (metadata.query) {
        return (
          <div className="space-y-1">
            <div className="text-zinc-300 text-xs">
              <span className="text-zinc-500">query:</span> "{metadata.query}"
            </div>
            {metadata.limit && (
              <div className="text-zinc-400 text-xs">
                <span className="text-zinc-500">limit:</span> {metadata.limit}
              </div>
            )}
          </div>
        );
      }

      if (metadata.messages && metadata.messages[0]) {
        const content = metadata.messages[0].content;
        return (
          <div className="space-y-1">
            <div className="text-zinc-300 text-xs">
              <span className="text-zinc-500">content:</span> "{content.length > 30 ? content.substring(0, 30) + '...' : content}"
            </div>
          </div>
        );
      }

      // 显示其他metadata
      const metadataStr = JSON.stringify(metadata, null, 0);
      return (
        <code className="text-xs text-zinc-400 bg-zinc-800/50 px-2 py-1 rounded">
          {metadataStr.length > 40 ? metadataStr.substring(0, 40) + '...' : metadataStr}
        </code>
      );
    }

    // API数据的metadata为null，显示系统信息
    return (
      <div className="space-y-1">
        <div className="text-zinc-500 text-xs italic">
          系统测试数据
        </div>
        {item.memory_id && (
          <div className="text-zinc-400 text-xs">
            <span className="text-zinc-500">memory:</span> {item.memory_id.substring(0, 8)}...
          </div>
        )}
      </div>
    );
  };

  // 排序功能
  const handleSort = (field: typeof sortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  // 排序活动数据
  const sortedActivities = [...activities].sort((a, b) => {
    let aValue: any = a[sortField];
    let bValue: any = b[sortField];

    if (sortField === 'timestamp') {
      aValue = new Date(aValue).getTime();
      bValue = new Date(bValue).getTime();
    }

    if (sortDirection === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  return (
    <Card className="bg-zinc-900 border-zinc-800">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-white flex items-center gap-2">
            <Activity className="w-5 h-5 text-[#00d4aa]" />
            活动时间线
            <span className="text-xs text-zinc-500 font-normal ml-2">(API数据 - 系统测试记录)</span>
            {loading && <Loader2 className="w-4 h-4 animate-spin text-[#00d4aa]" />}
          </CardTitle>
          <div className="flex items-center gap-2">
            {error && (
              <span className="text-red-400 text-sm mr-2">
                {error}
              </span>
            )}
            <Button
              variant="outline"
              size="sm"
              className="border-zinc-700 hover:border-zinc-600"
            >
              <Filter className="w-4 h-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="border-zinc-700 hover:border-zinc-600"
              onClick={loadActivities}
              disabled={loading}
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-zinc-800">
                <th 
                  className="text-left p-3 text-xs font-medium text-zinc-400 uppercase tracking-wider cursor-pointer hover:text-zinc-300 transition-colors"
                  onClick={() => handleSort('timestamp')}
                >
                  <div className="flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    Time
                    {sortField === 'timestamp' && (
                      sortDirection === 'desc' ? <ChevronDown className="w-3 h-3" /> : <ChevronUp className="w-3 h-3" />
                    )}
                  </div>
                </th>
                <th 
                  className="text-left p-3 text-xs font-medium text-zinc-400 uppercase tracking-wider cursor-pointer hover:text-zinc-300 transition-colors"
                  onClick={() => handleSort('operation')}
                >
                  <div className="flex items-center gap-1">
                    <Play className="w-3 h-3" />
                    Event Type
                    {sortField === 'operation' && (
                      sortDirection === 'desc' ? <ChevronDown className="w-3 h-3" /> : <ChevronUp className="w-3 h-3" />
                    )}
                  </div>
                </th>
                <th 
                  className="text-left p-3 text-xs font-medium text-zinc-400 uppercase tracking-wider cursor-pointer hover:text-zinc-300 transition-colors"
                  onClick={() => handleSort('user_id')}
                >
                  <div className="flex items-center gap-1">
                    <User className="w-3 h-3" />
                    User ID
                    {sortField === 'user_id' && (
                      sortDirection === 'desc' ? <ChevronDown className="w-3 h-3" /> : <ChevronUp className="w-3 h-3" />
                    )}
                  </div>
                </th>
                <th className="text-left p-3 text-xs font-medium text-zinc-400 uppercase tracking-wider">
                  <div className="flex items-center gap-1">
                    <Database className="w-3 h-3" />
                    Metadata
                  </div>
                </th>
                <th className="text-left p-3 text-xs font-medium text-zinc-400 uppercase tracking-wider">
                  <div className="flex items-center gap-1">
                    <Zap className="w-3 h-3" />
                    Latency
                  </div>
                </th>
                <th 
                  className="text-left p-3 text-xs font-medium text-zinc-400 uppercase tracking-wider cursor-pointer hover:text-zinc-300 transition-colors"
                  onClick={() => handleSort('status')}
                >
                  <div className="flex items-center gap-1">
                    <CheckCircle className="w-3 h-3" />
                    Status
                    {sortField === 'status' && (
                      sortDirection === 'desc' ? <ChevronDown className="w-3 h-3" /> : <ChevronUp className="w-3 h-3" />
                    )}
                  </div>
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-zinc-800">
              {sortedActivities.map((item) => {
                const operationInfo = getOperationInfo(item.operation);
                return (
                  <tr key={item.id} className="hover:bg-zinc-800/30 transition-colors">
                    <td className="p-3 text-sm text-zinc-300 font-mono">
                      {formatTimestamp(item.timestamp)}
                    </td>
                    <td className="p-3">
                      <Badge className={`${operationInfo.color} border text-xs`}>
                        {operationInfo.text}
                      </Badge>
                    </td>
                    <td className="p-3 text-sm text-zinc-300 font-mono">
                      {item.user_id ? (
                        <span className="text-[#00d4aa]">{item.user_id}</span>
                      ) : (
                        <span className="text-zinc-500">-</span>
                      )}
                    </td>
                    <td className="p-3 text-sm text-zinc-300 max-w-xs">
                      <div className="max-w-xs">
                        <CollapsibleMetadata metadata={item.metadata} maxPreviewLength={40} />
                      </div>
                    </td>
                    <td className="p-3 text-sm text-zinc-300 font-mono">
                      <span className={item.response_time ? 'text-[#00d4aa]' : 'text-zinc-500'}>
                        {item.response_time || '-'}
                      </span>
                    </td>
                    <td className="p-3">
                      <Badge className={`${getStatusColor(item.status)} border text-xs`}>
                        {item.status}
                      </Badge>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
};

export default ActivityTimelineReal;
