import { ActivityFilterConfig } from '@/components/mem0/ActivityFilter';

export interface ActivityItem {
  id: string;
  timestamp: string;
  operation: string;
  details: string;
  user_id?: string;
  memory_id?: string;
  metadata?: any;
  response_time?: string;
  status: string;
}

/**
 * 检查活动是否为UI操作
 */
export const isUIOperation = (activity: ActivityItem): boolean => {
  const uiIndicators = [
    'page_load', 'page_refresh', 'ui_load', 'component_mount',
    'route_change', 'tab_switch', 'filter_change', 'sort_change',
    'pagination', 'scroll', 'resize', 'focus', 'blur'
  ];
  
  const details = activity.details?.toLowerCase() || '';
  const metadata = activity.metadata;
  
  // 检查详情中是否包含UI操作关键词
  if (uiIndicators.some(indicator => details.includes(indicator))) {
    return true;
  }
  
  // 检查metadata中的UI标识
  if (metadata) {
    if (metadata.source === 'ui' || metadata.type === 'ui_operation') {
      return true;
    }
    
    // 检查是否为自动触发的搜索（如页面加载时的搜索）
    if (activity.operation === 'SEARCH' && metadata.trigger === 'auto') {
      return true;
    }
  }
  
  // 检查用户ID是否为系统用户
  const systemUsers = ['system', 'ui', 'auto', 'background'];
  if (activity.user_id && systemUsers.includes(activity.user_id.toLowerCase())) {
    return true;
  }
  
  return false;
};

/**
 * 检查活动是否为系统操作
 */
export const isSystemOperation = (activity: ActivityItem): boolean => {
  const systemIndicators = [
    'heartbeat', 'health_check', 'cleanup', 'maintenance',
    'backup', 'sync', 'migration', 'index_update'
  ];
  
  const details = activity.details?.toLowerCase() || '';
  const metadata = activity.metadata;
  
  // 检查详情中是否包含系统操作关键词
  if (systemIndicators.some(indicator => details.includes(indicator))) {
    return true;
  }
  
  // 检查metadata中的系统标识
  if (metadata) {
    if (metadata.source === 'system' || metadata.type === 'system_operation') {
      return true;
    }
  }
  
  return false;
};

/**
 * 检查活动是否有空的metadata
 */
export const hasEmptyMetadata = (activity: ActivityItem): boolean => {
  const metadata = activity.metadata;
  
  if (!metadata) return true;
  
  if (typeof metadata === 'object') {
    return Object.keys(metadata).length === 0;
  }
  
  if (typeof metadata === 'string') {
    return metadata.trim() === '' || metadata === '{}' || metadata === 'null';
  }
  
  return false;
};

/**
 * 检查活动是否包含排除关键词
 */
export const containsExcludeKeywords = (activity: ActivityItem, keywords: string[]): boolean => {
  if (keywords.length === 0) return false;
  
  const searchText = [
    activity.details,
    activity.operation,
    activity.user_id,
    JSON.stringify(activity.metadata)
  ].join(' ').toLowerCase();
  
  return keywords.some(keyword => 
    searchText.includes(keyword.toLowerCase())
  );
};

/**
 * 检查活动是否包含包含关键词
 */
export const containsIncludeKeywords = (activity: ActivityItem, keywords: string[]): boolean => {
  if (keywords.length === 0) return true; // 如果没有包含关键词，则通过
  
  const searchText = [
    activity.details,
    activity.operation,
    activity.user_id,
    JSON.stringify(activity.metadata)
  ].join(' ').toLowerCase();
  
  return keywords.some(keyword => 
    searchText.includes(keyword.toLowerCase())
  );
};

/**
 * 检查活动是否在时间范围内
 */
export const isWithinTimeRange = (activity: ActivityItem, timeRange: string): boolean => {
  if (timeRange === 'all') return true;
  
  const now = new Date();
  const activityTime = new Date(activity.timestamp);
  
  const timeRangeMap: Record<string, number> = {
    '1h': 1 * 60 * 60 * 1000,
    '24h': 24 * 60 * 60 * 1000,
    '7d': 7 * 24 * 60 * 60 * 1000,
    '30d': 30 * 24 * 60 * 60 * 1000
  };
  
  const rangeMs = timeRangeMap[timeRange];
  if (!rangeMs) return true;
  
  return (now.getTime() - activityTime.getTime()) <= rangeMs;
};

/**
 * 检查活动是否为重复搜索
 */
export const isDuplicateSearch = (activity: ActivityItem, allActivities: ActivityItem[], index: number): boolean => {
  if (activity.operation !== 'SEARCH') return false;
  
  // 检查前面是否有相同的搜索
  const previousActivities = allActivities.slice(0, index);
  const recentSearches = previousActivities
    .filter(a => a.operation === 'SEARCH')
    .slice(-5); // 只检查最近5次搜索
  
  return recentSearches.some(prevActivity => {
    // 检查是否为相同用户的相同搜索
    if (prevActivity.user_id === activity.user_id) {
      // 检查搜索内容是否相似
      const prevDetails = prevActivity.details?.toLowerCase() || '';
      const currentDetails = activity.details?.toLowerCase() || '';
      
      if (prevDetails === currentDetails) return true;
      
      // 检查metadata中的查询是否相同
      if (prevActivity.metadata?.query && activity.metadata?.query) {
        return prevActivity.metadata.query === activity.metadata.query;
      }
    }
    
    return false;
  });
};

/**
 * 检查响应时间是否在范围内
 */
export const isResponseTimeInRange = (activity: ActivityItem, minTime: number, maxTime: number): boolean => {
  if (!activity.response_time) return true;
  
  const responseTime = parseFloat(activity.response_time.replace(/[^\d.]/g, ''));
  if (isNaN(responseTime)) return true;
  
  return responseTime >= minTime && responseTime <= maxTime;
};

/**
 * 主要的活动过滤函数
 */
export const filterActivities = (
  activities: ActivityItem[], 
  config: ActivityFilterConfig
): ActivityItem[] => {
  return activities.filter((activity, index) => {
    // 操作类型过滤
    const operationMap: Record<string, boolean> = {
      'ADD': config.showAdd,
      'SEARCH': config.showSearch,
      'UPDATE': config.showUpdate,
      'DELETE': config.showDelete
    };
    
    if (operationMap[activity.operation] === false) {
      return false;
    }
    
    // 智能过滤
    if (config.hideUIOperations && isUIOperation(activity)) {
      return false;
    }
    
    if (config.hideSystemOperations && isSystemOperation(activity)) {
      return false;
    }
    
    if (config.hideEmptyMetadata && hasEmptyMetadata(activity)) {
      return false;
    }
    
    if (config.hideDuplicateSearches && isDuplicateSearch(activity, activities, index)) {
      return false;
    }
    
    // 时间范围过滤
    if (!isWithinTimeRange(activity, config.timeRange)) {
      return false;
    }
    
    // 用户过滤
    if (config.hideAnonymousUsers && (!activity.user_id || activity.user_id.trim() === '')) {
      return false;
    }
    
    if (config.specificUsers.length > 0 && 
        (!activity.user_id || !config.specificUsers.includes(activity.user_id))) {
      return false;
    }
    
    // 响应时间过滤
    if (!isResponseTimeInRange(activity, config.minResponseTime, config.maxResponseTime)) {
      return false;
    }
    
    // 关键词过滤
    if (containsExcludeKeywords(activity, config.excludeKeywords)) {
      return false;
    }
    
    if (!containsIncludeKeywords(activity, config.includeKeywords)) {
      return false;
    }
    
    return true;
  });
};

/**
 * 计算活动统计信息
 */
export const calculateActivityStats = (
  originalActivities: ActivityItem[],
  filteredActivities: ActivityItem[]
) => {
  const operations = originalActivities.reduce((acc, activity) => {
    acc[activity.operation] = (acc[activity.operation] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  return {
    total: originalActivities.length,
    filtered: filteredActivities.length,
    operations
  };
};
