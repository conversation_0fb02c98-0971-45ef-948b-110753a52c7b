<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Metadata 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #1a1a1a;
            color: #e5e5e5;
            padding: 20px;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-item {
            background-color: #2a2a2a;
            border: 1px solid #444;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .metadata-preview {
            background-color: #333;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Metadata 显示测试</h1>
        
        <div class="test-item">
            <h3>测试1: 简单的搜索metadata</h3>
            <div class="metadata-preview">
                {"query_type": "semantic", "results_count": 16, "index_used": "vector"}
            </div>
        </div>
        
        <div class="test-item">
            <h3>测试2: 复杂的metadata</h3>
            <div class="metadata-preview">
                {
                  "query_type": "semantic",
                  "results_count": 16,
                  "index_used": "vector",
                  "search_params": {
                    "limit": 50,
                    "threshold": 0.7,
                    "filters": ["category:user", "active:true"]
                  },
                  "performance": {
                    "query_time": "226ms",
                    "total_docs": 1024,
                    "matched_docs": 16
                  }
                }
            </div>
        </div>
        
        <div class="test-item">
            <h3>测试3: 添加内存的metadata</h3>
            <div class="metadata-preview">
                {"memory_type": "user", "content_length": 91, "embedding_model": "text-embedding-ada-002"}
            </div>
        </div>
    </div>
    
    <script>
        console.log('Metadata测试页面已加载');
        
        // 模拟API调用测试
        fetch('http://localhost:8000/v1/activities/?limit=10')
            .then(response => response.json())
            .then(data => {
                console.log('API返回的活动数据:', data);
                
                // 查找有metadata的记录
                const withMetadata = data.activities.filter(activity => activity.metadata);
                console.log('有metadata的记录:', withMetadata);
                
                if (withMetadata.length > 0) {
                    console.log('第一个有metadata的记录:', withMetadata[0]);
                    console.log('Metadata内容:', JSON.stringify(withMetadata[0].metadata, null, 2));
                }
            })
            .catch(error => {
                console.error('API调用失败:', error);
            });
    </script>
</body>
</html>
