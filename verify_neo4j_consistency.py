#!/usr/bin/env python3
"""
Neo4j图数据一致性验证脚本
验证Neo4j数据库中的图关系与API响应的一致性
"""

import requests
import json
import time
from typing import Dict, List, Any
from neo4j import GraphDatabase
import os

class Neo4jConsistencyVerifier:
    def __init__(self, base_url: str = "http://localhost:8000", neo4j_uri: str = "bolt://localhost:7687"):
        self.base_url = base_url
        self.neo4j_uri = neo4j_uri
        self.neo4j_user = "neo4j"
        self.neo4j_password = "mem0graph"
        self.user_id = "joseph_test"
        
    def test_api_health(self) -> bool:
        """测试API健康状态"""
        try:
            response = requests.get(f"{self.base_url}/health")
            print(f"✅ API健康状态: {response.status_code}")
            return response.status_code == 200
        except Exception as e:
            print(f"❌ API连接失败: {e}")
            return False
    
    def test_neo4j_connection(self) -> bool:
        """测试Neo4j连接"""
        try:
            driver = GraphDatabase.driver(self.neo4j_uri, auth=(self.neo4j_user, self.neo4j_password))
            with driver.session() as session:
                result = session.run("RETURN 1 as test")
                record = result.single()
                if record and record["test"] == 1:
                    print("✅ Neo4j连接成功")
                    driver.close()
                    return True
                else:
                    print("❌ Neo4j连接测试失败")
                    driver.close()
                    return False
        except Exception as e:
            print(f"❌ Neo4j连接失败: {e}")
            return False
    
    def get_api_memories_with_graph(self) -> Dict:
        """通过API获取记忆及图关系"""
        url = f"{self.base_url}/v1/memories/"
        params = {
            "user_id": self.user_id,
            "enable_graph": True,
            "output_format": "v1.1"
        }
        
        try:
            response = requests.get(url, params=params)
            if response.status_code == 200:
                result = response.json()
                if isinstance(result, list):
                    result = {"results": result, "relations": []}
                return result
            else:
                print(f"❌ API获取记忆失败: {response.status_code}")
                return {}
        except Exception as e:
            print(f"❌ API请求异常: {e}")
            return {}
    
    def get_neo4j_user_relationships(self) -> List[Dict]:
        """从Neo4j获取用户关系"""
        try:
            driver = GraphDatabase.driver(self.neo4j_uri, auth=(self.neo4j_user, self.neo4j_password))
            with driver.session() as session:
                # 获取用户的所有关系
                query = """
                MATCH (u:__User__ {user_id: $user_id})-[r]->(target)
                RETURN u.name as source, type(r) as relationship, target.name as target, 
                       labels(target) as target_labels, target.type as target_type
                """
                result = session.run(query, user_id=self.user_id)
                relationships = []
                for record in result:
                    relationships.append({
                        "source": record["source"],
                        "source_type": "person",
                        "relationship": record["relationship"],
                        "target": record["target"],
                        "target_type": record["target_type"] or record["target_labels"][0] if record["target_labels"] else "unknown"
                    })
                
                # 获取目标节点之间的关系
                query2 = """
                MATCH (source)-[r]->(target)
                WHERE (source)-[:__User__ {user_id: $user_id}]->(source) OR 
                      (target)-[:__User__ {user_id: $user_id}]->(target)
                RETURN source.name as source, type(r) as relationship, target.name as target,
                       labels(source) as source_labels, labels(target) as target_labels
                """
                result2 = session.run(query2, user_id=self.user_id)
                for record in result2:
                    if record["source"] != f"user_id:_{self.user_id}":  # 避免重复
                        relationships.append({
                            "source": record["source"],
                            "source_type": record["source_labels"][0] if record["source_labels"] else "unknown",
                            "relationship": record["relationship"],
                            "target": record["target"],
                            "target_type": record["target_labels"][0] if record["target_labels"] else "unknown"
                        })
                
                driver.close()
                return relationships
        except Exception as e:
            print(f"❌ Neo4j查询异常: {e}")
            return []
    
    def compare_relationships(self, api_relations: List[Dict], neo4j_relations: List[Dict]) -> Dict:
        """比较API和Neo4j的关系数据"""
        print(f"\n📊 关系数据比较:")
        print(f"   API关系数: {len(api_relations)}")
        print(f"   Neo4j关系数: {len(neo4j_relations)}")
        
        # 创建关系映射用于比较
        api_relation_map = {}
        for rel in api_relations:
            key = f"{rel.get('source', '')}-{rel.get('relationship', '')}-{rel.get('target', '')}"
            api_relation_map[key] = rel
        
        neo4j_relation_map = {}
        for rel in neo4j_relations:
            key = f"{rel.get('source', '')}-{rel.get('relationship', '')}-{rel.get('target', '')}"
            neo4j_relation_map[key] = rel
        
        # 找出匹配的关系
        matched = []
        api_only = []
        neo4j_only = []
        
        for key in api_relation_map:
            if key in neo4j_relation_map:
                matched.append(key)
            else:
                api_only.append(key)
        
        for key in neo4j_relation_map:
            if key not in api_relation_map:
                neo4j_only.append(key)
        
        print(f"   ✅ 匹配关系数: {len(matched)}")
        print(f"   ⚠️ 仅API存在: {len(api_only)}")
        print(f"   ⚠️ 仅Neo4j存在: {len(neo4j_only)}")
        
        if api_only:
            print("   仅API存在的关系:")
            for key in api_only[:5]:  # 只显示前5个
                print(f"     {key}")
        
        if neo4j_only:
            print("   仅Neo4j存在的关系:")
            for key in neo4j_only[:5]:  # 只显示前5个
                print(f"     {key}")
        
        return {
            "matched": len(matched),
            "api_only": len(api_only),
            "neo4j_only": len(neo4j_only),
            "total_api": len(api_relations),
            "total_neo4j": len(neo4j_relations)
        }
    
    def verify_data_consistency(self):
        """验证数据一致性"""
        print("=" * 60)
        print("🔍 Neo4j图数据一致性验证")
        print("=" * 60)
        
        # 1. 健康检查
        if not self.test_api_health():
            print("❌ API服务不可用")
            return
        
        if not self.test_neo4j_connection():
            print("❌ Neo4j连接失败")
            return
        
        # 2. 获取API数据
        print("\n📋 获取API记忆数据...")
        api_data = self.get_api_memories_with_graph()
        api_relations = api_data.get('relations', [])
        
        # 3. 获取Neo4j数据
        print("📋 获取Neo4j图数据...")
        neo4j_relations = self.get_neo4j_user_relationships()
        
        # 4. 比较数据
        comparison = self.compare_relationships(api_relations, neo4j_relations)
        
        # 5. 一致性评估
        print("\n📊 一致性评估:")
        total_api = comparison["total_api"]
        total_neo4j = comparison["total_neo4j"]
        matched = comparison["matched"]
        
        if total_api > 0 and total_neo4j > 0:
            api_consistency = (matched / total_api) * 100
            neo4j_consistency = (matched / total_neo4j) * 100
            
            print(f"   API数据一致性: {api_consistency:.1f}%")
            print(f"   Neo4j数据一致性: {neo4j_consistency:.1f}%")
            
            if api_consistency >= 80 and neo4j_consistency >= 80:
                print("   ✅ 数据一致性良好")
            elif api_consistency >= 60 and neo4j_consistency >= 60:
                print("   ⚠️ 数据一致性一般，建议检查")
            else:
                print("   ❌ 数据一致性较差，需要修复")
        else:
            print("   ⚠️ 无法计算一致性（数据为空）")
        
        print("\n" + "=" * 60)
        print("✅ 一致性验证完成")
        print("=" * 60)

if __name__ == "__main__":
    verifier = Neo4jConsistencyVerifier()
    verifier.verify_data_consistency() 