#!/bin/bash

# 选择性重新提交脚本 - 重新整理提交 912d7e1e5342ca27dcb2792791b59292299c30b0
# 排除敏感信息文件、不必要的文档、.前缀的文件夹，以及mem0_ui下的.next和node_modules文件夹

set -e

COMMIT_HASH="912d7e1e5342ca27dcb2792791b59292299c30b0"
TEMP_BRANCH="temp_selective_rewrite_$(date +%s)"

echo "🚀 开始重新整理提交 $COMMIT_HASH，排除敏感文件"

# 检查当前是否在dev分支
current_branch=$(git branch --show-current)
if [ "$current_branch" != "dev" ]; then
    echo "❌ 错误：当前不在dev分支，请切换到dev分支后再运行此脚本"
    exit 1
fi

# 确保工作目录干净
if ! git diff --quiet || ! git diff --cached --quiet; then
    echo "❌ 错误：工作目录不干净，请先提交或暂存更改"
    exit 1
fi

# 检查当前HEAD是否就是目标提交
current_head=$(git rev-parse HEAD)
if [ "$current_head" != "$COMMIT_HASH" ]; then
    echo "❌ 错误：当前HEAD不是目标提交 $COMMIT_HASH"
    echo "当前HEAD: $current_head"
    exit 1
fi

# 回退到上一个提交，但保留工作目录的更改
echo "📝 软重置到上一个提交..."
git reset --soft HEAD~1

# 获取当前暂存区的所有文件（即原提交的内容）
echo "📋 获取暂存区文件列表..."
git diff --cached --name-only > /tmp/commit_files.txt

# 定义排除模式
exclude_patterns=(
    # 敏感信息文件
    "*.env*"
    "*.key"
    "*.pem"
    "*.p12"
    "*.pfx"
    "config.json"
    "server/data/.mem0/config.json"
    "server/data/.mem0/migrations_qdrant/*"
    
    # .前缀的文件夹（但保留.gitignore等重要文件）
    ".cursor/*"
    ".vscode/*"
    ".idea/*"
    ".DS_Store"
    
    # mem0_ui下的构建产物和依赖
    "mem0_ui/.next/*"
    "mem0_ui/node_modules/*"
    "mem0_ui/.next"
    "mem0_ui/node_modules"
    
    # openmemory下的构建产物和依赖
    "openmemory/ui/.next/*"
    "openmemory/ui/node_modules/*"
    "openmemory/ui/.next"
    "openmemory/ui/node_modules"
    
    # 不必要的文档（保留README.md等重要文档）
    "*.backup*"
    "*.bak"
    "*.tmp"
    "*.log"
    "*.out"
    "*analysis.md"
    "docs/*"
    "*.tsbuildinfo"
    
    # 测试和临时文件
    "test_*.js"
    "test_*.py"
    "test_*.sh"
    "*test*.js"
    "*test*.py"
    "*test*.sh"
    "*.test.*"
    "__tests__/*"
    "jest.*"
    "*.spec.*"
    
    # 备份文件
    "backups/*"
    "*.backup_*"
    "main.py.backup_*"
)

# 创建包含文件列表
echo "🔍 筛选需要包含的文件..."
> /tmp/files_to_include.txt

while IFS= read -r file; do
    should_exclude=false
    
    # 检查是否匹配排除模式
    for pattern in "${exclude_patterns[@]}"; do
        if [[ "$file" == $pattern ]]; then
            should_exclude=true
            echo "❌ 排除: $file (匹配模式: $pattern)"
            break
        fi
    done
    
    # 如果不需要排除，添加到包含列表
    if [ "$should_exclude" = false ]; then
        echo "✅ 包含: $file"
        echo "$file" >> /tmp/files_to_include.txt
    fi
done < /tmp/commit_files.txt

# 检查是否有文件需要合并
if [ ! -s /tmp/files_to_include.txt ]; then
    echo "⚠️  警告：没有文件需要合并"
    git checkout dev
    git branch -D "$TEMP_BRANCH"
    exit 0
fi

echo ""
echo "📊 合并统计："
total_files=$(wc -l < /tmp/commit_files.txt)
included_files=$(wc -l < /tmp/files_to_include.txt)
excluded_files=$((total_files - included_files))

echo "  总文件数: $total_files"
echo "  包含文件数: $included_files"
echo "  排除文件数: $excluded_files"
echo ""

# 重新添加需要的文件到暂存区
echo "🔄 重新添加需要的文件到暂存区..."
git reset  # 清空暂存区

failed_files=()
while IFS= read -r file; do
    echo "处理文件: $file"

    # 检查文件是否存在于工作目录
    if [ -f "$file" ]; then
        git add "$file"
        echo "  ✅ 成功添加: $file"
    else
        echo "  ❌ 文件不存在: $file"
        failed_files+=("$file")
    fi
done < /tmp/files_to_include.txt

# 检查是否有失败的文件
if [ ${#failed_files[@]} -gt 0 ]; then
    echo ""
    echo "❌ 以下文件处理失败："
    for file in "${failed_files[@]}"; do
        echo "  - $file"
    done
    echo ""
    read -p "是否继续提交其他成功的文件？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "取消操作，恢复原始提交"
        git reset --hard "$COMMIT_HASH"
        exit 1
    fi
fi

# 提交更改
if git diff --cached --quiet; then
    echo "⚠️  没有更改需要提交，恢复原始提交"
    git reset --hard "$COMMIT_HASH"
    exit 0
fi

echo "💾 创建新的清理后的提交..."
commit_message="优化webui大部分数据不同步的问题，优化了核心的分类、上下文、高级检索、自定义指令功能

原始提交: $COMMIT_HASH
包含文件: $included_files/$total_files (排除敏感信息、构建产物、测试文件等)

- 排除了敏感配置文件和数据库文件
- 排除了构建产物(.next, node_modules等)
- 排除了测试文件和临时文件
- 排除了备份文件和文档分析文件"

git commit -m "$commit_message"

# 清理临时文件
rm -f /tmp/commit_files.txt /tmp/files_to_include.txt

echo ""
echo "🎉 选择性重新提交完成！"
echo "📊 最终统计："
echo "  - 原始提交: $COMMIT_HASH"
echo "  - 总文件数: $total_files"
echo "  - 成功包含: $included_files 个文件"
echo "  - 排除文件: $excluded_files 个文件"

if [ ${#failed_files[@]} -gt 0 ]; then
    echo "  - 失败文件: ${#failed_files[@]} 个"
fi

echo ""
echo "✅ 重新提交已完成，敏感文件已被排除"
echo "🔍 请检查结果并进行测试"
echo ""
echo "📋 被排除的文件类型："
echo "  - 敏感配置文件 (*.env*, config.json等)"
echo "  - 构建产物 (.next/, node_modules/等)"
echo "  - 测试文件 (*test*, __tests__/等)"
echo "  - 备份文件 (*.backup*, backups/等)"
echo "  - 临时文件 (*.tmp, *.log等)"
