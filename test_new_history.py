#!/usr/bin/env python3
"""
测试新的历史记录功能

使用新的HistoryManager来生成测试记录，验证修复是否有效
"""

import sys
import os
sys.path.append('/opt/mem0ai')

from mem0.memory.history_manager import HistoryManager, HistoryManagerFactory
from datetime import datetime
import uuid

def test_history_manager():
    """测试新的HistoryManager功能"""
    
    print("🧪 测试新的HistoryManager功能...")
    
    try:
        # 创建HistoryManager实例
        history_manager = HistoryManagerFactory.create_history_manager(
            "/opt/mem0ai/server/data/mem0/history.db"
        )
        
        print("✅ HistoryManager创建成功")
        
        # 测试ADD操作记录
        print("\n📝 测试ADD操作记录...")
        add_id = history_manager.record_add_memory(
            memory_id=str(uuid.uuid4()),
            content="这是一个测试记忆：我今天学习了Python编程",
            actor_id="test_user_001",
            role="user",
            memory_type="user",
            embedding_model="text-embedding-ada-002",
            vector_dimension=1536,
            is_async=False
        )
        print(f"✅ ADD记录创建成功，ID: {add_id}")
        
        # 测试UPDATE操作记录
        print("\n🔄 测试UPDATE操作记录...")
        update_id = history_manager.record_update_memory(
            memory_id=str(uuid.uuid4()),
            old_content="旧的记忆内容",
            new_content="更新后的记忆内容：我今天学习了Python和机器学习",
            actor_id="test_user_001", 
            role="user",
            memory_type="user",
            embedding_model="text-embedding-ada-002",
            is_async=False
        )
        print(f"✅ UPDATE记录创建成功，ID: {update_id}")
        
        # 测试DELETE操作记录
        print("\n🗑️ 测试DELETE操作记录...")
        delete_id = history_manager.record_delete_memory(
            memory_id=str(uuid.uuid4()),
            deleted_content="要删除的记忆内容",
            actor_id="test_user_001",
            role="user",
            memory_type="user",
            cascade=False,
            backup_created=True,
            references_count=2,
            is_async=False
        )
        print(f"✅ DELETE记录创建成功，ID: {delete_id}")
        
        # 测试SEARCH操作记录
        print("\n🔍 测试SEARCH操作记录...")
        search_id = history_manager.record_search_operation(
            search_id=str(uuid.uuid4()),
            query="搜索Python相关的记忆",
            actor_id="test_user_001",
            role="user",
            query_type="semantic",
            results_count=5,
            filters={"user_id": "test_user_001", "memory_type": "user"},
            limit=10,
            is_async=False
        )
        print(f"✅ SEARCH记录创建成功，ID: {search_id}")
        
        print("\n🎉 所有操作记录测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def verify_new_records():
    """验证新生成的记录是否包含完整数据"""
    
    print("\n🔍 验证新生成的记录...")
    
    import sqlite3
    
    try:
        conn = sqlite3.connect("/opt/mem0ai/server/data/mem0/history.db")
        cursor = conn.cursor()
        
        # 查询最近创建的记录
        cursor.execute("""
            SELECT id, event, role, metadata, created_at, actor_id
            FROM history 
            WHERE created_at > datetime('now', '-1 minute')
            ORDER BY created_at DESC
        """)
        
        recent_records = cursor.fetchall()
        
        if not recent_records:
            print("❌ 没有找到最近创建的记录")
            return False
        
        print(f"📊 找到 {len(recent_records)} 条最近创建的记录:")
        
        all_complete = True
        
        for record in recent_records:
            record_id, event, role, metadata, created_at, actor_id = record
            
            # 检查字段完整性
            role_ok = role is not None and role != ""
            metadata_ok = metadata is not None and metadata != ""
            actor_ok = actor_id is not None and actor_id != ""
            
            status_role = "✅" if role_ok else "❌"
            status_metadata = "✅" if metadata_ok else "❌"
            status_actor = "✅" if actor_ok else "❌"
            
            print(f"  - {record_id[:8]}... ({event}): role={status_role}, metadata={status_metadata}, actor={status_actor}")
            
            if not (role_ok and metadata_ok and actor_ok):
                all_complete = False
        
        conn.close()
        
        if all_complete:
            print("✅ 所有新记录数据完整")
        else:
            print("❌ 部分新记录数据不完整")
        
        return all_complete
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


def main():
    """主函数"""
    
    print("🚀 测试新的历史记录功能")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 测试HistoryManager功能
    test_ok = test_history_manager()
    
    if test_ok:
        # 验证新记录
        verify_ok = verify_new_records()
        
        print("\n" + "=" * 60)
        print("📋 测试结果汇总:")
        print(f"   - HistoryManager功能: {'✅ 通过' if test_ok else '❌ 失败'}")
        print(f"   - 新记录完整性: {'✅ 通过' if verify_ok else '❌ 失败'}")
        
        if test_ok and verify_ok:
            print("\n🎉 恭喜！新的历史记录功能工作正常")
            return 0
        else:
            print("\n⚠️  部分测试失败")
            return 1
    else:
        print("\n❌ HistoryManager功能测试失败")
        return 1


if __name__ == "__main__":
    sys.exit(main())