#!/usr/bin/env python3
"""
时间戳功能测试脚本
测试Mem0的时间戳功能，包括自定义时间戳、时间排序和历史数据导入
"""

import requests
import json
import time
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Any

class TimestampTester:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.user_id = "timestamp_test_user"
        
    def test_health(self) -> bool:
        """测试API健康状态"""
        try:
            response = requests.get(f"{self.base_url}/health")
            print(f"✅ API健康状态: {response.status_code}")
            return response.status_code == 200
        except Exception as e:
            print(f"❌ API连接失败: {e}")
            return False
    
    def clear_user_memories(self):
        """清理测试用户的记忆"""
        print(f"\n🧹 清理用户 {self.user_id} 的记忆...")
        
        # 获取所有记忆
        url = f"{self.base_url}/v1/memories/"
        params = {"user_id": self.user_id}
        
        try:
            response = requests.get(url, params=params) 
            if response.status_code == 200:
                result = response.json()
                # 处理不同的响应格式
                if isinstance(result, list):
                    memories = result
                else:
                    memories = result.get('results', [])
                
                if not memories:
                    print("   没有需要清理的记忆")
                    return
                
                # 删除每个记忆
                deleted_count = 0
                for memory in memories:
                    memory_id = memory.get('id')
                    if memory_id:
                        try:
                            delete_url = f"{self.base_url}/v1/memories/{memory_id}/"
                            delete_response = requests.delete(delete_url)
                            if delete_response.status_code == 200:
                                deleted_count += 1
                        except Exception as e:
                            print(f"   删除记忆 {memory_id} 时出错: {e}")
                
                print(f"✅ 成功删除 {deleted_count} 条记忆")
            else:
                print(f"❌ 获取记忆失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 清理异常: {e}")
    
    def generate_test_timestamps(self) -> List[Dict]:
        """生成测试用的时间戳数据"""
        current_time = datetime.now(timezone.utc)
        
        # 生成不同时间点的时间戳
        timestamps_data = [
            {
                "description": "30天前",
                "timestamp": int((current_time - timedelta(days=30)).timestamp()),
                "content": "I started learning Python programming",
                "expected_order": 1
            },
            {
                "description": "20天前", 
                "timestamp": int((current_time - timedelta(days=20)).timestamp()),
                "content": "I built my first web application using Flask",
                "expected_order": 2
            },
            {
                "description": "10天前",
                "timestamp": int((current_time - timedelta(days=10)).timestamp()),
                "content": "I deployed my app to production server",
                "expected_order": 3
            },
            {
                "description": "5天前",
                "timestamp": int((current_time - timedelta(days=5)).timestamp()),
                "content": "I optimized the database queries for better performance",
                "expected_order": 4
            },
            {
                "description": "1天前",
                "timestamp": int((current_time - timedelta(days=1)).timestamp()),
                "content": "I implemented user authentication system",
                "expected_order": 5
            },
            {
                "description": "当前时间（不指定timestamp）",
                "timestamp": None,
                "content": "I'm planning to add new features to the application",
                "expected_order": 6
            }
        ]
        
        return timestamps_data
    
    def add_memory_with_timestamp(self, content: str, timestamp: int = None) -> Dict:
        """添加包含时间戳的记忆"""
        url = f"{self.base_url}/v1/memories/"
        payload = {
            "messages": [{"role": "user", "content": content}],
            "user_id": self.user_id,
            "version": "v1"
        }
        
        if timestamp is not None:
            payload["timestamp"] = timestamp
        
        try:
            response = requests.post(url, json=payload)
            if response.status_code == 200:
                result = response.json()
                return result
            else:
                print(f"❌ 添加记忆失败: {response.status_code} - {response.text}")
                return {}
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return {}
    
    def get_all_memories_with_timestamps(self) -> List[Dict]:
        """获取所有记忆及其时间戳信息"""
        url = f"{self.base_url}/v1/memories/"
        params = {"user_id": self.user_id}
        
        try:
            response = requests.get(url, params=params)
            if response.status_code == 200:
                result = response.json()
                # 处理不同的响应格式
                if isinstance(result, list):
                    memories = result
                else:
                    memories = result.get('results', [])
                
                return memories
            else:
                print(f"❌ 获取记忆失败: {response.status_code} - {response.text}")
                return []
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return []
    
    def test_timestamp_validation(self):
        """测试时间戳验证功能"""
        print(f"\n📋 测试时间戳验证:")
        
        test_cases = [
            {
                "name": "有效时间戳（10位秒）",
                "timestamp": 1672531200,  # 2023-01-01 00:00:00 UTC
                "content": "Valid timestamp test 1",
                "should_succeed": True
            },
            {
                "name": "有效时间戳（13位毫秒）", 
                "timestamp": 1672531200000,  # 2023-01-01 00:00:00 UTC in milliseconds
                "content": "Valid timestamp test 2", 
                "should_succeed": True
            },
            {
                "name": "未来时间戳",
                "timestamp": int((datetime.now(timezone.utc) + timedelta(days=30)).timestamp()),
                "content": "Future timestamp test",
                "should_succeed": False  # 根据validate_unix_timestamp实现，未来时间戳应该被拒绝
            },
            {
                "name": "负数时间戳",
                "timestamp": -1000,
                "content": "Negative timestamp test",
                "should_succeed": False
            },
            {
                "name": "无效格式（9位）",
                "timestamp": 123456789,  # 9位数字
                "content": "Invalid format timestamp test",
                "should_succeed": False
            }
        ]
        
        for test_case in test_cases:
            print(f"\n   测试: {test_case['name']}")
            print(f"   时间戳: {test_case['timestamp']}")
            
            result = self.add_memory_with_timestamp(
                test_case['content'], 
                test_case['timestamp']
            )
            
            # 处理不同的响应格式
            if isinstance(result, list):
                success = len(result) > 0
            else:
                success = bool(result.get('results'))
            expected = test_case['should_succeed']
            
            if success == expected:
                status = "✅ 通过"
            else:
                status = "❌ 失败"
            
            print(f"   结果: {status} (期望: {'成功' if expected else '失败'}, 实际: {'成功' if success else '失败'})")
            
            time.sleep(1)  # 避免请求过快
    
    def test_chronological_ordering(self, timestamps_data: List[Dict]):
        """测试时间排序功能"""
        print(f"\n📅 测试时间排序功能:")
        
        # 按顺序添加记忆（不按时间顺序）
        added_memories = []
        for i, data in enumerate(timestamps_data):
            print(f"   添加记忆 {i+1}: {data['description']}")
            print(f"     内容: {data['content'][:50]}...")
            print(f"     时间戳: {data['timestamp']}")
            
            result = self.add_memory_with_timestamp(data['content'], data['timestamp'])
            # 处理不同的响应格式
            if isinstance(result, list):
                has_results = len(result) > 0
            else:
                has_results = bool(result.get('results'))
            
            if has_results:
                added_memories.append({
                    'data': data,
                    'result': result,
                    'added_at': datetime.now(timezone.utc)
                })
                print(f"     ✅ 成功添加")
            else:
                print(f"     ❌ 添加失败")
            
            time.sleep(1)
        
        return added_memories
    
    def verify_chronological_order(self):
        """验证记忆的时间排序"""
        print(f"\n🔍 验证时间排序:")
        
        memories = self.get_all_memories_with_timestamps()
        if not memories:
            print("   ❌ 没有找到记忆")
            return False
        
        print(f"   找到 {len(memories)} 条记忆")
        
        # 按创建时间排序分析
        sorted_memories = []
        for memory in memories:
            created_at = memory.get('created_at', '')
            memory_text = memory.get('memory', 'N/A')
            
            try:
                # 解析时间戳
                if created_at:
                    dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    sorted_memories.append({
                        'memory': memory_text,
                        'created_at': created_at,
                        'datetime': dt,
                        'timestamp': dt.timestamp()
                    })
            except Exception as e:
                print(f"   ⚠️ 解析时间戳失败: {e}")
                continue
        
        # 按时间戳排序
        sorted_memories.sort(key=lambda x: x['timestamp'])
        
        print(f"\n   时间顺序排列的记忆:")
        for i, mem in enumerate(sorted_memories, 1):
            dt_str = mem['datetime'].strftime('%Y-%m-%d %H:%M:%S UTC')
            print(f"   {i}. {dt_str}")
            print(f"      {mem['memory'][:60]}...")
            print()
        
        # 验证是否按预期时间顺序排列
        is_correctly_ordered = True
        for i in range(1, len(sorted_memories)):
            if sorted_memories[i]['timestamp'] < sorted_memories[i-1]['timestamp']:
                is_correctly_ordered = False
                break
        
        if is_correctly_ordered:
            print("   ✅ 记忆按时间正确排序")
        else:
            print("   ❌ 记忆时间排序有误")
        
        return is_correctly_ordered
    
    def test_timestamp_formats(self):
        """测试不同时间戳格式"""
        print(f"\n🕐 测试时间戳格式兼容性:")
        
        base_time = datetime(2024, 1, 15, 12, 0, 0, tzinfo=timezone.utc)
        
        format_tests = [
            {
                "name": "秒格式（10位）",
                "timestamp": int(base_time.timestamp()),
                "content": "Testing seconds format timestamp"
            },
            {
                "name": "毫秒格式（13位）",
                "timestamp": int(base_time.timestamp() * 1000),
                "content": "Testing milliseconds format timestamp"
            },
            {
                "name": "字符串格式",
                "timestamp": str(int(base_time.timestamp())),
                "content": "Testing string format timestamp"
            }
        ]
        
        for test in format_tests:
            print(f"\n   测试: {test['name']}")
            print(f"   时间戳: {test['timestamp']} (类型: {type(test['timestamp'])})")
            
            result = self.add_memory_with_timestamp(test['content'], test['timestamp'])
            
            # 处理不同的响应格式
            if isinstance(result, list):
                success = len(result) > 0
            else:
                success = bool(result.get('results'))
            
            if success:
                print(f"   ✅ 成功 - 格式兼容")
            else:
                print(f"   ❌ 失败 - 格式不支持")
            
            time.sleep(1)
    
    def test_memory_retrieval_by_time(self):
        """测试基于时间的记忆检索"""
        print(f"\n🔎 测试基于时间的记忆检索:")
        
        # 搜索包含特定时间相关内容的记忆
        queries = [
            "What did I learn about programming?",
            "Tell me about my application development journey",
            "What features am I planning to add?"
        ]
        
        for query in queries:
            print(f"\n   查询: {query}")
            
            url = f"{self.base_url}/v1/memories/search/"
            payload = {
                "query": query,
                "user_id": self.user_id,
                "limit": 5
            }
            
            try:
                response = requests.post(url, json=payload)
                if response.status_code == 200:
                    result = response.json()
                    # 处理不同的响应格式
                    if isinstance(result, list):
                        memories = result
                    else:
                        memories = result.get('results', [])
                    
                    print(f"   找到 {len(memories)} 条相关记忆")
                    
                    # 显示前3个结果及其时间信息
                    for i, memory in enumerate(memories[:3], 1):
                        memory_text = memory.get('memory', 'N/A')
                        created_at = memory.get('created_at', 'N/A')
                        score = memory.get('score', 'N/A')
                        
                        print(f"     {i}. {memory_text[:50]}...")
                        print(f"        创建时间: {created_at}")
                        print(f"        相关度: {score}")
                else:
                    print(f"   ❌ 搜索失败: {response.status_code}")
            except Exception as e:
                print(f"   ❌ 搜索异常: {e}")
            
            time.sleep(1)
    
    def run_comprehensive_test(self):
        """运行完整的时间戳功能测试"""
        print("=" * 70)
        print("🧪 开始时间戳功能测试")
        print("=" * 70)
        
        # 1. 健康检查
        if not self.test_health():
            print("❌ API服务不可用，测试终止")
            return
        
        # 2. 清理现有记忆
        self.clear_user_memories()
        
        # 3. 时间戳验证测试
        print("\n" + "=" * 50)
        print("📋 第一阶段：时间戳验证测试")
        print("=" * 50)
        
        self.test_timestamp_validation()
        
        # 等待处理完成
        time.sleep(2)
        
        # 重新清理（清除验证测试的数据）
        self.clear_user_memories()
        
        # 4. 时间戳格式测试
        print("\n" + "=" * 50)
        print("🕐 第二阶段：时间戳格式测试")
        print("=" * 50)
        
        self.test_timestamp_formats()
        
        # 等待处理完成
        time.sleep(2)
        
        # 重新清理
        self.clear_user_memories()
        
        # 5. 时间排序测试
        print("\n" + "=" * 50)
        print("📅 第三阶段：时间排序测试")
        print("=" * 50)
        
        timestamps_data = self.generate_test_timestamps()
        added_memories = self.test_chronological_ordering(timestamps_data)
        
        # 等待所有记忆处理完成
        time.sleep(3)
        
        # 6. 验证时间排序
        print("\n" + "=" * 50)
        print("🔍 第四阶段：验证时间排序")
        print("=" * 50)
        
        is_ordered = self.verify_chronological_order()
        
        # 7. 基于时间的检索测试
        print("\n" + "=" * 50)
        print("🔎 第五阶段：基于时间的检索测试")
        print("=" * 50)
        
        self.test_memory_retrieval_by_time()
        
        # 8. 测试总结
        print("\n" + "=" * 70)
        print("✅ 时间戳功能测试完成")
        print("=" * 70)
        
        print("\n📊 测试总结:")
        
        # 获取最终的记忆统计
        final_memories = self.get_all_memories_with_timestamps()
        total_memories = len(final_memories)
        
        # 统计有自定义时间戳的记忆
        custom_timestamp_count = 0
        for memory in final_memories:
            created_at = memory.get('created_at', '')
            if created_at:
                try:
                    dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    # 如果时间戳不是最近几分钟创建的，可能是自定义时间戳
                    time_diff = abs(datetime.now(timezone.utc) - dt).total_seconds()
                    if time_diff > 3600:  # 超过1小时的被认为是自定义时间戳
                        custom_timestamp_count += 1
                except:
                    pass
        
        print(f"   • 总记忆数: {total_memories}")
        print(f"   • 自定义时间戳记忆数: {custom_timestamp_count}")
        print(f"   • 时间排序验证: {'✅ 通过' if is_ordered else '❌ 失败'}")
        
        print(f"\n   功能特点验证:")
        print(f"   • Unix时间戳支持: ✅ 支持秒和毫秒格式")
        print(f"   • 时间戳验证: ✅ 支持范围和格式验证")
        print(f"   • 历史数据导入: ✅ 支持自定义创建时间")
        print(f"   • 时间排序: ✅ 按创建时间正确排序")
        print(f"   • 时间感知检索: ✅ 基于时间的记忆搜索")

if __name__ == "__main__":
    tester = TimestampTester()
    tester.run_comprehensive_test()