#!/usr/bin/env python3
"""
记忆操作数据一致性测试脚本

测试添加、搜索、删除、更新操作的数据一致性，
验证历史记录与向量存储之间的数据对应关系。
"""

import sys
import os
import sqlite3
import uuid
import time
from datetime import datetime
from typing import Dict, List, Any

# 添加项目路径
sys.path.append('/opt/mem0ai')

from mem0 import Memory
from mem0.memory.storage import SQLiteManager

class MemoryOperationsConsistencyTest:
    """记忆操作数据一致性测试类"""
    
    def __init__(self):
        """初始化测试环境"""
        self.memory = None
        self.db_manager = None
        self.test_user_id = f"test_user_{int(time.time())}"
        self.test_memories = []
        self.test_results = {
            "add": {"success": 0, "failed": 0, "details": []},
            "search": {"success": 0, "failed": 0, "details": []},
            "update": {"success": 0, "failed": 0, "details": []},
            "delete": {"success": 0, "failed": 0, "details": []},
            "consistency": {"success": 0, "failed": 0, "details": []}
        }
        
    def setup(self):
        """设置测试环境"""
        try:
            print("🔧 设置测试环境...")
            
            # 初始化Memory实例
            self.memory = Memory()
            
            # 初始化数据库管理器
            db_path = "/opt/mem0ai/server/data/mem0/history.db"
            self.db_manager = SQLiteManager(db_path)
            
            print(f"✅ 测试环境设置完成，用户ID: {self.test_user_id}")
            return True
            
        except Exception as e:
            print(f"❌ 设置测试环境失败: {e}")
            return False
    
    def test_add_operations(self):
        """测试添加操作的数据一致性"""
        print("\n📝 测试添加操作...")
        
        test_messages = [
            "我喜欢吃披萨和意大利面",
            "我的爱好是阅读科幻小说",
            "我在一家科技公司工作，职位是软件工程师",
            "我计划明年去日本旅游",
            "我最喜欢的编程语言是Python"
        ]
        
        for i, message in enumerate(test_messages):
            try:
                # 执行添加操作
                result = self.memory.add(
                    messages=[{"role": "user", "content": message}],
                    user_id=self.test_user_id,
                    metadata={"test_index": i, "operation": "add_test"}
                )
                
                if result and "results" in result and result["results"]:
                    memory_item = result["results"][0]
                    memory_id = memory_item["id"]
                    
                    # 验证向量存储中的数据
                    vector_memory = self.memory.get(memory_id)
                    if vector_memory:
                        # 验证历史记录
                        history_records = self.db_manager.get_history(memory_id)
                        
                        if self._validate_add_consistency(memory_item, vector_memory, history_records, message):
                            self.test_results["add"]["success"] += 1
                            self.test_memories.append({
                                "id": memory_id,
                                "content": message,
                                "operation": "add"
                            })
                            print(f"  ✅ 添加操作 {i+1} 数据一致性验证通过")
                        else:
                            self.test_results["add"]["failed"] += 1
                            print(f"  ❌ 添加操作 {i+1} 数据一致性验证失败")
                    else:
                        self.test_results["add"]["failed"] += 1
                        print(f"  ❌ 添加操作 {i+1} 向量存储验证失败")
                else:
                    self.test_results["add"]["failed"] += 1
                    print(f"  ❌ 添加操作 {i+1} 执行失败")
                    
            except Exception as e:
                self.test_results["add"]["failed"] += 1
                self.test_results["add"]["details"].append(f"添加操作 {i+1} 异常: {e}")
                print(f"  ❌ 添加操作 {i+1} 异常: {e}")
        
        print(f"📊 添加操作测试完成: {self.test_results['add']['success']} 成功, {self.test_results['add']['failed']} 失败")
    
    def test_search_operations(self):
        """测试搜索操作的数据一致性"""
        print("\n🔍 测试搜索操作...")
        
        search_queries = [
            "喜欢的食物",
            "工作相关",
            "兴趣爱好",
            "旅游计划",
            "编程语言"
        ]
        
        for i, query in enumerate(search_queries):
            try:
                # 执行搜索操作
                search_result = self.memory.search(
                    query=query,
                    user_id=self.test_user_id,
                    limit=10
                )
                
                if search_result and "results" in search_result:
                    # 验证搜索历史记录
                    recent_search_records = self._get_recent_search_records()
                    
                    if self._validate_search_consistency(query, search_result, recent_search_records):
                        self.test_results["search"]["success"] += 1
                        print(f"  ✅ 搜索操作 {i+1} 数据一致性验证通过")
                    else:
                        self.test_results["search"]["failed"] += 1
                        print(f"  ❌ 搜索操作 {i+1} 数据一致性验证失败")
                else:
                    self.test_results["search"]["failed"] += 1
                    print(f"  ❌ 搜索操作 {i+1} 执行失败")
                    
            except Exception as e:
                self.test_results["search"]["failed"] += 1
                self.test_results["search"]["details"].append(f"搜索操作 {i+1} 异常: {e}")
                print(f"  ❌ 搜索操作 {i+1} 异常: {e}")
        
        print(f"📊 搜索操作测试完成: {self.test_results['search']['success']} 成功, {self.test_results['search']['failed']} 失败")
    
    def test_update_operations(self):
        """测试更新操作的数据一致性"""
        print("\n🔄 测试更新操作...")
        
        if not self.test_memories:
            print("  ⚠️  没有可用的测试记忆进行更新")
            return
        
        # 选择前3个记忆进行更新测试
        memories_to_update = self.test_memories[:3]
        
        for i, memory_info in enumerate(memories_to_update):
            try:
                memory_id = memory_info["id"]
                new_content = f"更新后的内容: {memory_info['content']} (已修改)"
                
                # 获取更新前的状态
                old_memory = self.memory.get(memory_id)
                old_history = self.db_manager.get_history(memory_id)
                
                # 执行更新操作
                update_result = self.memory.update(
                    memory_id=memory_id,
                    data=new_content,
                    metadata={"updated_test": True}
                )
                
                if update_result:
                    # 验证更新后的状态
                    updated_memory = self.memory.get(memory_id)
                    updated_history = self.db_manager.get_history(memory_id)
                    
                    if self._validate_update_consistency(memory_id, old_memory, updated_memory, 
                                                       old_history, updated_history, new_content):
                        self.test_results["update"]["success"] += 1
                        print(f"  ✅ 更新操作 {i+1} 数据一致性验证通过")
                    else:
                        self.test_results["update"]["failed"] += 1
                        print(f"  ❌ 更新操作 {i+1} 数据一致性验证失败")
                else:
                    self.test_results["update"]["failed"] += 1
                    print(f"  ❌ 更新操作 {i+1} 执行失败")
                    
            except Exception as e:
                self.test_results["update"]["failed"] += 1
                self.test_results["update"]["details"].append(f"更新操作 {i+1} 异常: {e}")
                print(f"  ❌ 更新操作 {i+1} 异常: {e}")
        
        print(f"📊 更新操作测试完成: {self.test_results['update']['success']} 成功, {self.test_results['update']['failed']} 失败")
    
    def test_delete_operations(self):
        """测试删除操作的数据一致性"""
        print("\n🗑️ 测试删除操作...")
        
        if len(self.test_memories) < 2:
            print("  ⚠️  没有足够的测试记忆进行删除")
            return
        
        # 选择最后2个记忆进行删除测试
        memories_to_delete = self.test_memories[-2:]
        
        for i, memory_info in enumerate(memories_to_delete):
            try:
                memory_id = memory_info["id"]
                
                # 获取删除前的状态
                old_memory = self.memory.get(memory_id)
                old_history = self.db_manager.get_history(memory_id)
                
                # 执行删除操作
                delete_result = self.memory.delete(memory_id)
                
                if delete_result:
                    # 验证删除后的状态
                    deleted_memory = self.memory.get(memory_id)
                    updated_history = self.db_manager.get_history(memory_id)
                    
                    if self._validate_delete_consistency(memory_id, old_memory, deleted_memory,
                                                       old_history, updated_history):
                        self.test_results["delete"]["success"] += 1
                        print(f"  ✅ 删除操作 {i+1} 数据一致性验证通过")
                    else:
                        self.test_results["delete"]["failed"] += 1
                        print(f"  ❌ 删除操作 {i+1} 数据一致性验证失败")
                else:
                    self.test_results["delete"]["failed"] += 1
                    print(f"  ❌ 删除操作 {i+1} 执行失败")
                    
            except Exception as e:
                self.test_results["delete"]["failed"] += 1
                self.test_results["delete"]["details"].append(f"删除操作 {i+1} 异常: {e}")
                print(f"  ❌ 删除操作 {i+1} 异常: {e}")
        
        print(f"📊 删除操作测试完成: {self.test_results['delete']['success']} 成功, {self.test_results['delete']['failed']} 失败")
    
    def _validate_add_consistency(self, memory_item, vector_memory, history_records, original_content):
        """验证添加操作的数据一致性"""
        try:
            # 检查基本信息一致性
            if memory_item["id"] != vector_memory["id"]:
                self.test_results["add"]["details"].append("内存ID不一致")
                return False
            
            if memory_item["memory"] != vector_memory["memory"]:
                self.test_results["add"]["details"].append("内存内容不一致")
                return False
            
            # 检查历史记录
            if not history_records:
                self.test_results["add"]["details"].append("缺失历史记录")
                return False
            
            # 检查ADD事件记录
            add_records = [r for r in history_records if r.get("event") == "ADD"]
            if not add_records:
                self.test_results["add"]["details"].append("缺失ADD历史事件")
                return False
            
            add_record = add_records[-1]  # 最新的ADD记录
            
            # 验证历史记录完整性
            required_fields = ["role", "metadata"]
            for field in required_fields:
                if not add_record.get(field):
                    self.test_results["add"]["details"].append(f"历史记录缺失{field}字段")
                    return False
            
            return True
            
        except Exception as e:
            self.test_results["add"]["details"].append(f"添加一致性验证异常: {e}")
            return False
    
    def _validate_search_consistency(self, query, search_result, recent_search_records):
        """验证搜索操作的数据一致性"""
        try:
            # 检查是否有最近的搜索记录
            if not recent_search_records:
                self.test_results["search"]["details"].append("缺失搜索历史记录")
                return False
            
            # 查找匹配的搜索记录
            matching_record = None
            for record in recent_search_records:
                if record.get("new_memory") == query:
                    matching_record = record
                    break
            
            if not matching_record:
                self.test_results["search"]["details"].append("未找到匹配的搜索历史记录")
                return False
            
            # 验证搜索记录完整性
            required_fields = ["role", "metadata"]
            for field in required_fields:
                if not matching_record.get(field):
                    self.test_results["search"]["details"].append(f"搜索历史记录缺失{field}字段")
                    return False
            
            # 验证metadata中的搜索信息
            try:
                import json
                metadata = json.loads(matching_record.get("metadata", "{}"))
                if not metadata.get("query_type") or not metadata.get("results_count") is not None:
                    self.test_results["search"]["details"].append("搜索metadata信息不完整")
                    return False
            except:
                self.test_results["search"]["details"].append("搜索metadata格式错误")
                return False
            
            return True
            
        except Exception as e:
            self.test_results["search"]["details"].append(f"搜索一致性验证异常: {e}")
            return False
    
    def _validate_update_consistency(self, memory_id, old_memory, updated_memory, 
                                   old_history, updated_history, new_content):
        """验证更新操作的数据一致性"""
        try:
            # 检查内存确实被更新
            if updated_memory["memory"] != new_content:
                self.test_results["update"]["details"].append("内存内容未正确更新")
                return False
            
            # 检查历史记录数量增加
            if len(updated_history) <= len(old_history):
                self.test_results["update"]["details"].append("更新后历史记录数量未增加")
                return False
            
            # 检查UPDATE事件记录
            update_records = [r for r in updated_history if r.get("event") == "UPDATE"]
            if not update_records:
                self.test_results["update"]["details"].append("缺失UPDATE历史事件")
                return False
            
            update_record = update_records[-1]  # 最新的UPDATE记录
            
            # 验证UPDATE记录完整性
            required_fields = ["role", "metadata", "old_memory", "new_memory"]
            for field in required_fields:
                if not update_record.get(field):
                    self.test_results["update"]["details"].append(f"UPDATE历史记录缺失{field}字段")
                    return False
            
            # 验证更新内容
            if update_record.get("new_memory") != new_content:
                self.test_results["update"]["details"].append("UPDATE记录中新内容不匹配")
                return False
            
            return True
            
        except Exception as e:
            self.test_results["update"]["details"].append(f"更新一致性验证异常: {e}")
            return False
    
    def _validate_delete_consistency(self, memory_id, old_memory, deleted_memory,
                                   old_history, updated_history):
        """验证删除操作的数据一致性"""
        try:
            # 检查内存确实被删除（应该返回None）
            if deleted_memory is not None:
                self.test_results["delete"]["details"].append("内存未被正确删除")
                return False
            
            # 检查历史记录数量增加
            if len(updated_history) <= len(old_history):
                self.test_results["delete"]["details"].append("删除后历史记录数量未增加")
                return False
            
            # 检查DELETE事件记录
            delete_records = [r for r in updated_history if r.get("event") == "DELETE"]
            if not delete_records:
                self.test_results["delete"]["details"].append("缺失DELETE历史事件")
                return False
            
            delete_record = delete_records[-1]  # 最新的DELETE记录
            
            # 验证DELETE记录完整性
            required_fields = ["role", "metadata", "old_memory"]
            for field in required_fields:
                if not delete_record.get(field):
                    self.test_results["delete"]["details"].append(f"DELETE历史记录缺失{field}字段")
                    return False
            
            # 验证is_deleted标志
            if delete_record.get("is_deleted") != 1:
                self.test_results["delete"]["details"].append("DELETE记录is_deleted标志错误")
                return False
            
            return True
            
        except Exception as e:
            self.test_results["delete"]["details"].append(f"删除一致性验证异常: {e}")
            return False
    
    def _get_recent_search_records(self):
        """获取最近的搜索记录"""
        try:
            conn = sqlite3.connect("/opt/mem0ai/server/data/mem0/history.db")
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM history 
                WHERE event = 'SEARCH' 
                AND created_at > datetime('now', '-1 minute')
                ORDER BY created_at DESC
                LIMIT 10
            """)
            
            columns = [description[0] for description in cursor.description]
            records = []
            for row in cursor.fetchall():
                record = dict(zip(columns, row))
                records.append(record)
            
            conn.close()
            return records
            
        except Exception as e:
            print(f"获取搜索记录失败: {e}")
            return []
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "="*80)
        print("📋 记忆操作数据一致性测试报告")
        print("="*80)
        
        total_success = sum(result["success"] for result in self.test_results.values())
        total_failed = sum(result["failed"] for result in self.test_results.values())
        total_tests = total_success + total_failed
        
        success_rate = (total_success / total_tests * 100) if total_tests > 0 else 0
        
        print(f"📊 总体统计:")
        print(f"   - 总测试数: {total_tests}")
        print(f"   - 成功: {total_success}")
        print(f"   - 失败: {total_failed}")
        print(f"   - 成功率: {success_rate:.1f}%")
        
        print(f"\n📝 详细结果:")
        operations = ["add", "search", "update", "delete"]
        operation_names = ["添加操作", "搜索操作", "更新操作", "删除操作"]
        
        for op, name in zip(operations, operation_names):
            result = self.test_results[op]
            total = result["success"] + result["failed"]
            if total > 0:
                rate = result["success"] / total * 100
                status = "✅" if rate == 100 else "⚠️" if rate >= 80 else "❌"
                print(f"   {status} {name}: {result['success']}/{total} ({rate:.1f}%)")
                
                # 显示错误详情
                if result["details"]:
                    print(f"     错误详情:")
                    for detail in result["details"][:3]:  # 只显示前3个错误
                        print(f"       - {detail}")
        
        # 数据库状态检查
        print(f"\n🔍 数据库状态检查:")
        try:
            conn = sqlite3.connect("/opt/mem0ai/server/data/mem0/history.db")
            cursor = conn.cursor()
            
            # 检查最近1分钟的记录完整性
            cursor.execute("""
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN role IS NOT NULL AND role != '' THEN 1 ELSE 0 END) as with_role,
                    SUM(CASE WHEN metadata IS NOT NULL AND metadata != '' THEN 1 ELSE 0 END) as with_metadata,
                    SUM(CASE WHEN actor_id IS NOT NULL AND actor_id != '' THEN 1 ELSE 0 END) as with_actor
                FROM history 
                WHERE created_at > datetime('now', '-1 minute')
            """)
            
            stats = cursor.fetchone()
            if stats and stats[0] > 0:
                total, with_role, with_metadata, with_actor = stats
                print(f"   - 最近记录总数: {total}")
                print(f"   - 包含role字段: {with_role}/{total} ({with_role/total*100:.1f}%)")
                print(f"   - 包含metadata字段: {with_metadata}/{total} ({with_metadata/total*100:.1f}%)")
                print(f"   - 包含actor_id字段: {with_actor}/{total} ({with_actor/total*100:.1f}%)")
            
            conn.close()
            
        except Exception as e:
            print(f"   数据库状态检查失败: {e}")
        
        # 测试结论
        print(f"\n🎯 测试结论:")
        if success_rate >= 95:
            print("   🟢 数据一致性测试表现优秀，系统运行稳定")
        elif success_rate >= 80:
            print("   🟡 数据一致性测试表现良好，有少量问题需要关注")
        else:
            print("   🔴 数据一致性测试发现严重问题，需要进一步修复")
        
        return success_rate
    
    def cleanup(self):
        """清理测试环境"""
        try:
            if self.db_manager:
                self.db_manager.close()
            print("🧹 测试环境清理完成")
        except Exception as e:
            print(f"⚠️  清理测试环境时出现问题: {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始记忆操作数据一致性测试")
        print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        if not self.setup():
            return False
        
        try:
            # 按顺序执行所有测试
            self.test_add_operations()
            time.sleep(1)  # 等待数据同步
            
            self.test_search_operations()
            time.sleep(1)
            
            self.test_update_operations()
            time.sleep(1)
            
            self.test_delete_operations()
            time.sleep(1)
            
            # 生成报告
            success_rate = self.generate_report()
            
            return success_rate >= 80
            
        finally:
            self.cleanup()


def main():
    """主函数"""
    test = MemoryOperationsConsistencyTest()
    success = test.run_all_tests()
    
    if success:
        print("\n🎉 记忆操作数据一致性测试通过！")
        return 0
    else:
        print("\n⚠️  记忆操作数据一致性测试发现问题！")
        return 1


if __name__ == "__main__":
    sys.exit(main())