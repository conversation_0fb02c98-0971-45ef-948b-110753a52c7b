#!/bin/bash

# 批量修复API端点格式的脚本

echo "🔧 批量修复API端点格式"
echo "===================="

# 需要修复的端点列表 (不带斜杠 -> 带斜杠)
declare -A endpoints=(
    ["/health"]="/health/"
    ["/configure"]="/configure/"
    ["/cache/clear"]="/cache/clear/"
    ["/cache/status"]="/cache/status/"
    ["/reset"]="/reset/"
    ["/v1/activities"]="/v1/activities/"
    ["/v1/admin/dashboard"]="/v1/admin/dashboard/"
    ["/v1/exports/get"]="/v1/exports/get/"
    ["/v1/graph/entities"]="/v1/graph/entities/"
    ["/v1/graph/entities/{entity_id}"]="/v1/graph/entities/{entity_id}/"
    ["/v1/graph/relationships"]="/v1/graph/relationships/"
    ["/v1/graph/relationships/{relationship_id}"]="/v1/graph/relationships/{relationship_id}/"
    ["/v1/graph/search"]="/v1/graph/search/"
    ["/v1/graph/stats"]="/v1/graph/stats/"
    ["/v1/graph/visualization/{memory_id}"]="/v1/graph/visualization/{memory_id}/"
    ["/v1/stats"]="/v1/stats/"
    ["/v1/users"]="/v1/users/"
    ["/v1/users/batch"]="/v1/users/batch/"
    ["/v1/users/{user_id}"]="/v1/users/{user_id}/"
    ["/v1/users/{user_id}/analytics"]="/v1/users/{user_id}/analytics/"
    ["/v1/users/{user_id}/export"]="/v1/users/{user_id}/export/"
    ["/v1/users/{user_id}/stats"]="/v1/users/{user_id}/stats/"
)

echo "📋 将修复 ${#endpoints[@]} 个端点"

# 创建备份
cp /opt/mem0ai/server/main.py /opt/mem0ai/server/main.py.backup_before_api_fix

echo "✅ 已创建备份: main.py.backup_before_api_fix"

# 应用修复
for old_endpoint in "${!endpoints[@]}"; do
    new_endpoint="${endpoints[$old_endpoint]}"
    echo "  $old_endpoint -> $new_endpoint"
    
    # 转义特殊字符用于sed
    old_escaped=$(echo "$old_endpoint" | sed 's/[[\.*^$()+?{|]/\\&/g')
    new_escaped=$(echo "$new_endpoint" | sed 's/[[\.*^$()+?{|]/\\&/g')
    
    # 执行替换 (只替换在@app.method()中的路径)
    sed -i "s|@app\.\(get\|post\|put\|delete\|patch\)(\"${old_escaped}\"|@app.\1(\"${new_escaped}\"|g" /opt/mem0ai/server/main.py
done

echo ""
echo "🔍 验证修复结果..."
echo "修复后的端点格式:"

# 检查修复结果
echo "✅ 带尾部斜杠的端点:"
grep -o '@app\.\(get\|post\|put\|delete\|patch\)("[^"]*/"' /opt/mem0ai/server/main.py | sed 's/@app\.[^(]*("/  /' | sed 's/"$//' | sort -u | wc -l

echo "❌ 仍不带尾部斜杠的端点:"
remaining=$(grep -o '@app\.\(get\|post\|put\|delete\|patch\)("[^"]*[^/]"' /opt/mem0ai/server/main.py | sed 's/@app\.[^(]*("/  /' | sed 's/"$//' | sort -u)
if [ -z "$remaining" ]; then
    echo "  无 - 全部已修复!"
else
    echo "$remaining"
fi

echo ""
echo "🎯 API端点格式统一完成!"