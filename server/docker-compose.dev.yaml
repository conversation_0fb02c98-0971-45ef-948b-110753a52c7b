# =============================================================================
# Mem0 Docker 配置 - 开发环境
# 用于开发环境的Docker Compose配置，支持代码热重载和数据持久化
# =============================================================================

name: mem0-dev

services:
  # Mem0 API 服务 - 开发配置
  mem0-api:
    build:
      context: ..  # 构建上下文为项目根目录
      dockerfile: server/Dockerfile
    container_name: mem0-api-dev
    ports:
      - "${API_PORT:-8000}:8000"
    env_file:
      - .env
    networks:
      - mem0-network
    volumes:
      # 代码挂载 - 用于开发热重载
      - ./:/app/src                        # 服务器源码
      - ../mem0:/app/packages/mem0         # Mem0库源码
      - ../mem0_ui:/app/ui                 # UI源码（如果需要）
      
      # 数据持久化 - 使用主机目录便于开发调试
      - ./data:/app/data:rw                # 应用数据目录
      - ./logs:/app/logs:rw                # 应用日志目录
      - ./config:/app/config:rw            # 配置文件目录
      
      # 开发工具（可选）
      # - ~/.gitconfig:/root/.gitconfig:ro  # Git配置
    depends_on:
      qdrant:
        condition: service_healthy
      neo4j:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "python", "-c", "import urllib.request; urllib.request.urlopen('http://localhost:8000/health', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    # 简化权限处理，去除复杂的用户切换
    environment:
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
      - PYTHONWARNINGS=ignore
      - PYTHONPATH=/app/src:/app/packages
      - MEM0_DATA_PATH=/app/data
      - MEM0_DIR=/app/data/mem0
      - HISTORY_DB_PATH=/app/data/mem0/history.db
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USERNAME=neo4j
      - NEO4J_PASSWORD=mem0graph
      - ENABLE_GRAPH_STORE=${ENABLE_GRAPH_STORE:-true}
      - TZ=Asia/Shanghai
      # 开发环境特定配置
      - ENVIRONMENT=development
      - DEBUG=${DEBUG:-true}
      - LOG_LEVEL=${LOG_LEVEL:-DEBUG}
      - RELOAD_ON_CHANGE=true
    command: >
      bash -c "
        cd /app/src &&
        uvicorn main:app 
        --host 0.0.0.0 
        --port 8000 
        --reload 
        --reload-dir /app/src 
        --reload-dir /app/packages/mem0
        --log-level debug
      "
    working_dir: /app/src
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '0.5'

  # Qdrant 向量数据库
  qdrant:
    image: qdrant/qdrant:v1.11.3
    container_name: mem0-qdrant-dev
    networks:
      - mem0-network
    ports:
      - "${QDRANT_PORT:-6333}:6333"
      - "${QDRANT_GRPC_PORT:-6334}:6334"
    volumes:
      - ./data/qdrant:/qdrant/storage     # 使用主机目录方便开发调试
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "timeout 5 bash -c 'echo > /dev/tcp/localhost/6333' || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
      - QDRANT__LOG_LEVEL=DEBUG           # 开发环境使用调试日志
      - TZ=Asia/Shanghai
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.25'

  # Neo4j 图数据库
  neo4j:
    image: neo4j:5.26.0
    container_name: mem0-neo4j-dev
    networks:
      - mem0-network
    healthcheck:
      test: ["CMD", "cypher-shell", "-u", "neo4j", "-p", "mem0graph", "RETURN 1"]
      interval: 30s
      timeout: 10s
      retries: 10
      start_period: 60s
    ports:
      - "${NEO4J_HTTP_PORT:-7474}:7474"  # HTTP 端口
      - "${NEO4J_BOLT_PORT:-7687}:7687"  # Bolt 端口
    volumes:
      - ./data/neo4j/data:/data           # 使用主机目录
      - ./data/neo4j/logs:/logs
      - ./data/neo4j/import:/var/lib/neo4j/import
      - ./data/neo4j/plugins:/plugins
    restart: unless-stopped
    environment:
      - NEO4J_AUTH=${NEO4J_USERNAME:-neo4j}/${NEO4J_PASSWORD:-mem0graph}
      - NEO4J_PLUGINS=["apoc"]
      - NEO4J_apoc_export_file_enabled=true
      - NEO4J_apoc_import_file_enabled=true
      - NEO4J_apoc_import_file_use__neo4j__config=true
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*
      - NEO4J_dbms_security_procedures_allowlist=apoc.*
      # 开发环境内存配置（较小）
      - NEO4J_dbms_memory_heap_initial__size=512M
      - NEO4J_dbms_memory_heap_max__size=2G
      - NEO4J_dbms_memory_pagecache_size=1G
      - NEO4J_dbms_logs_debug_level=DEBUG  # 开发环境调试日志
      - TZ=Asia/Shanghai
    deploy:
      resources:
        limits:
          memory: 3G
          cpus: '1.5'
        reservations:
          memory: 1G
          cpus: '0.5'

  # 开发工具服务（可选）
  # 数据库管理工具
  adminer:
    image: adminer:4.8.1
    container_name: mem0-adminer-dev
    networks:
      - mem0-network
    ports:
      - "${ADMINER_PORT:-8080}:8080"
    environment:
      - ADMINER_DEFAULT_SERVER=mem0-api-dev
    restart: unless-stopped
    profiles: ["tools"]  # 使用 --profile tools 启动

# 网络配置
networks:
  mem0-network:
    driver: bridge
    name: mem0-dev-network