#!/bin/bash

# 修复版批量删除无分类记忆脚本

echo "🗑️ 批量删除无分类记忆 (修复版)"
echo "=================================="

# 获取所有用户
echo "📋 获取用户列表..."
users=$(curl -s "http://localhost:8000/v1/users?limit=100" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    for user in data.get('users', []):
        print(user['user_id'])
except:
    pass
")

total_deleted=0

for user_id in $users; do
    echo ""
    echo "🔍 处理用户: $user_id"
    
    # 获取该用户无分类的记忆IDs
    memory_ids=$(curl -s "http://localhost:8000/v1/memories/?user_id=$user_id&limit=1000" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    no_cat_ids = []
    with_cat_count = 0
    
    for item in data:
        if not item.get('categories') or len(item.get('categories', [])) == 0:
            no_cat_ids.append(item['id'])
        else:
            with_cat_count += 1
    
    print(f'COUNTS:{len(no_cat_ids)}:{with_cat_count}')
    for mem_id in no_cat_ids:
        print(mem_id)
except Exception as e:
    print('COUNTS:0:0')
")
    
    if [ -n "$memory_ids" ]; then
        counts_line=$(echo "$memory_ids" | head -n 1)
        no_cat_count=$(echo "$counts_line" | cut -d':' -f2)
        with_cat_count=$(echo "$counts_line" | cut -d':' -f3)
        
        echo "   📊 有分类: $with_cat_count 个，无分类: $no_cat_count 个"
        
        if [ "$no_cat_count" != "0" ]; then
            echo "   🗑️  开始删除..."
            delete_count=0
            
            # 获取要删除的ID列表 (跳过第一行的统计信息)
            ids_to_delete=$(echo "$memory_ids" | tail -n +2)
            
            for memory_id in $ids_to_delete; do
                if [ -n "$memory_id" ] && [ "$memory_id" != "COUNTS:0:0" ]; then
                    # 执行删除
                    response=$(curl -s -w "%{http_code}" -X DELETE "http://localhost:8000/v1/memories/$memory_id")
                    http_code="${response: -3}"
                    
                    if [ "$http_code" = "200" ]; then
                        ((delete_count++))
                        ((total_deleted++))
                    else
                        echo "     ❌ 删除失败: $memory_id (HTTP: $http_code)"
                    fi
                fi
            done
            
            echo "   ✅ 成功删除: $delete_count 个记忆"
        else
            echo "   ✅ 该用户所有记忆都有分类"
        fi
    else
        echo "   ℹ️  该用户无记忆数据"
    fi
done

echo ""
echo "🎯 批量删除完成！"
echo "📊 总计删除: $total_deleted 个无分类记忆"

# 验证结果
echo ""
echo "🔍 验证删除结果..."
for user_id in $users; do
    result=$(curl -s "http://localhost:8000/v1/memories/?user_id=$user_id&limit=1000" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    total = len(data)
    no_cat = sum(1 for item in data if not item.get('categories') or len(item.get('categories', [])) == 0)
    with_cat = total - no_cat
    
    if total == 0:
        print(f'ℹ️ {user_id}: 无记忆数据')
    elif no_cat == 0:
        print(f'✅ {user_id}: {with_cat}个记忆，全部有分类')
    else:
        print(f'⚠️ {user_id}: {with_cat}个有分类，{no_cat}个无分类 (需要重新处理)')
except:
    print(f'❌ {user_id}: 数据获取失败')
" 2>/dev/null)
    echo "$result"
done