# =============================================================================
# Mem0 Docker 配置 - 生产环境
# 用于生产环境的Docker Compose配置，优化性能和安全性
# =============================================================================

name: mem0-prod

services:
  # Mem0 API 服务 - 生产配置
  mem0-api:
    build:
      context: ..
      dockerfile: server/Dockerfile
      # target: production  # 简化构建，不使用多阶段
    image: mem0-api:${VERSION:-latest}
    container_name: mem0-api-prod
    ports:
      - "${API_PORT:-8000}:8000"
    env_file:
      - .env.prod
    networks:
      - mem0-network
    volumes:
      # 仅数据持久化 - 使用Docker命名卷提高性能
      - mem0-app-data:/app/data
      - mem0-app-logs:/app/logs
      - mem0-app-config:/app/config
    depends_on:
      qdrant:
        condition: service_healthy
      neo4j:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "python", "-c", "import urllib.request; urllib.request.urlopen('http://localhost:8000/health', timeout=5)"]
      interval: 15s
      timeout: 5s
      retries: 3
      start_period: 30s
    restart: always
    security_opt:
      - no-new-privileges:true
    read_only: true  # 只读文件系统提高安全性
    tmpfs:
      - /tmp:noexec,nosuid,size=100m
      - /var/tmp:noexec,nosuid,size=100m
    environment:
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
      - PYTHONWARNINGS=ignore
      - MEM0_DATA_PATH=/app/data
      - MEM0_DIR=/app/data/mem0
      - HISTORY_DB_PATH=/app/data/mem0/history.db
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USERNAME=neo4j
      - NEO4J_PASSWORD_FILE=/run/secrets/neo4j_password
      - ENABLE_GRAPH_STORE=true
      - TZ=Asia/Shanghai
      # 生产环境特定配置
      - ENVIRONMENT=production
      - DEBUG=false
      - LOG_LEVEL=INFO
      - WORKERS=${API_WORKERS:-4}
      - MAX_REQUESTS=${MAX_REQUESTS:-1000}
      - MAX_REQUESTS_JITTER=${MAX_REQUESTS_JITTER:-100}
    command: >
      gunicorn main:app
      --bind 0.0.0.0:8000
      --workers ${API_WORKERS:-4}
      --worker-class uvicorn.workers.UvicornWorker
      --max-requests ${MAX_REQUESTS:-1000}
      --max-requests-jitter ${MAX_REQUESTS_JITTER:-100}
      --access-logfile -
      --error-logfile -
      --log-level info
      --timeout 30
      --keep-alive 2
    secrets:
      - neo4j_password
      - openai_api_key
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s

  # Qdrant 向量数据库 - 生产配置
  qdrant:
    image: qdrant/qdrant:v1.11.3
    container_name: mem0-qdrant-prod
    networks:
      - mem0-network
    ports:
      - "127.0.0.1:${QDRANT_PORT:-6333}:6333"  # 仅本地访问
      - "127.0.0.1:${QDRANT_GRPC_PORT:-6334}:6334"
    volumes:
      - qdrant-data:/qdrant/storage
    restart: always
    healthcheck:
      test: ["CMD-SHELL", "timeout 5 bash -c 'echo > /dev/tcp/localhost/6333' || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    security_opt:
      - no-new-privileges:true
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
      - QDRANT__LOG_LEVEL=INFO
      - QDRANT__SERVICE__MAX_REQUEST_SIZE_MB=32
      - QDRANT__STORAGE__PERFORMANCE__MAX_SEARCH_THREADS=4
      - TZ=Asia/Shanghai
    deploy:
      resources:
        limits:
          memory: 8G
          cpus: '4.0'
        reservations:
          memory: 2G
          cpus: '1.0'

  # Neo4j 图数据库 - 生产配置
  neo4j:
    image: neo4j:5.26.0
    container_name: mem0-neo4j-prod
    networks:
      - mem0-network
    healthcheck:
      test: ["CMD", "cypher-shell", "-u", "neo4j", "-p", "mem0graph", "RETURN 1"]
      interval: 30s
      timeout: 10s
      retries: 10
      start_period: 60s
    ports:
      - "127.0.0.1:${NEO4J_HTTP_PORT:-7474}:7474"  # 仅本地访问
      - "127.0.0.1:${NEO4J_BOLT_PORT:-7687}:7687"
    volumes:
      - neo4j-data:/data
      - neo4j-logs:/logs
      - neo4j-import:/var/lib/neo4j/import
      - neo4j-plugins:/plugins
    restart: always
    security_opt:
      - no-new-privileges:true
    environment:
      - NEO4J_AUTH=neo4j/${NEO4J_PASSWORD}
      - NEO4J_PLUGINS=["apoc"]
      - NEO4J_apoc_export_file_enabled=true
      - NEO4J_apoc_import_file_enabled=true
      - NEO4J_apoc_import_file_use__neo4j__config=true
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*
      - NEO4J_dbms_security_procedures_allowlist=apoc.*
      # 生产环境内存配置（大内存）
      - NEO4J_dbms_memory_heap_initial__size=2G
      - NEO4J_dbms_memory_heap_max__size=8G
      - NEO4J_dbms_memory_pagecache_size=4G
      - NEO4J_dbms_logs_debug_level=WARN  # 生产环境警告级别日志
      # 性能调优
      - NEO4J_dbms_security_auth_enabled=true
      - NEO4J_dbms_connector_bolt_listen_address=0.0.0.0:7687
      - NEO4J_dbms_connector_http_listen_address=0.0.0.0:7474
      - NEO4J_dbms_tx_log_rotation_retention_policy=100M size
      - TZ=Asia/Shanghai
    secrets:
      - neo4j_password
    deploy:
      resources:
        limits:
          memory: 12G
          cpus: '4.0'
        reservations:
          memory: 4G
          cpus: '2.0'

  # Nginx 反向代理（生产环境推荐）
  nginx:
    image: nginx:1.25-alpine
    container_name: mem0-nginx-prod
    networks:
      - mem0-network
    ports:
      - "${HTTP_PORT:-80}:80"
      - "${HTTPS_PORT:-443}:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx-cache:/var/cache/nginx
      - nginx-logs:/var/log/nginx
    depends_on:
      - mem0-api
    restart: always
    healthcheck:
      test: ["CMD", "python", "-c", "import urllib.request; urllib.request.urlopen('http://localhost/health', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3
    profiles: ["nginx"]  # 使用 --profile nginx 启动
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.1'

  # 监控服务
  prometheus:
    image: prom/prometheus:v2.40.0
    container_name: mem0-prometheus-prod
    networks:
      - mem0-network
    ports:
      - "127.0.0.1:9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=15d'
      - '--web.enable-lifecycle'
    restart: always
    profiles: ["monitoring"]
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.25'

# Docker Secrets配置
secrets:
  neo4j_password:
    file: ./secrets/neo4j_password.txt
  openai_api_key:
    file: ./secrets/openai_api_key.txt

# 生产环境数据持久化的命名卷
volumes:
  # Mem0 应用数据
  mem0-app-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/lib/mem0/data

  mem0-app-logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/log/mem0

  mem0-app-config:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /etc/mem0

  # Qdrant 向量数据库
  qdrant-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/lib/qdrant

  # Neo4j 图数据库
  neo4j-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/lib/neo4j/data

  neo4j-logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/log/neo4j

  neo4j-import:
    driver: local

  neo4j-plugins:
    driver: local

  # Nginx
  nginx-cache:
    driver: local

  nginx-logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/log/nginx

  # 监控
  prometheus-data:
    driver: local

# 网络配置
networks:
  mem0-network:
    driver: bridge
    name: mem0-prod-network
    driver_opts:
      com.docker.network.bridge.name: mem0-prod-br