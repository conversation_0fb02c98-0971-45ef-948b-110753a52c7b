#!/usr/bin/env python3
"""
Mem0 数据完整性验证模块
用于在应用启动时和运行时验证数据完整性
"""

import os
import sys
import sqlite3
import logging
import json
from datetime import datetime
from typing import Dict, Any, List, Tuple, Optional
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class DataIntegrityValidator:
    """数据完整性验证器"""
    
    def __init__(self, data_path: str = None, history_db_path: str = None):
        self.data_path = Path(data_path or os.environ.get("MEM0_DATA_PATH", "/app/data"))
        self.history_db_path = Path(history_db_path or os.environ.get("HISTORY_DB_PATH", "/app/data/history.db"))
        self.validation_results = {}
        
    def validate_all(self) -> Dict[str, Any]:
        """执行完整的数据验证"""
        logger.info("Starting comprehensive data validation")
        
        results = {
            "timestamp": datetime.now().isoformat(),
            "data_path": str(self.data_path),
            "history_db_path": str(self.history_db_path),
            "validations": {},
            "summary": {
                "total_checks": 0,
                "passed_checks": 0,
                "failed_checks": 0,
                "warnings": 0
            }
        }
        
        # 执行各项验证
        validations = [
            ("directory_structure", self._validate_directory_structure),
            ("file_permissions", self._validate_file_permissions), 
            ("database_integrity", self._validate_database_integrity),
            ("data_consistency", self._validate_data_consistency),
            ("storage_health", self._validate_storage_health)
        ]
        
        for name, validator in validations:
            try:
                validation_result = validator()
                results["validations"][name] = validation_result
                
                # 更新汇总统计
                results["summary"]["total_checks"] += validation_result.get("total_checks", 0)
                results["summary"]["passed_checks"] += validation_result.get("passed_checks", 0)
                results["summary"]["failed_checks"] += validation_result.get("failed_checks", 0)
                results["summary"]["warnings"] += validation_result.get("warnings", 0)
                
            except Exception as e:
                logger.error(f"Validation {name} failed with error: {e}")
                results["validations"][name] = {
                    "status": "error",
                    "error": str(e),
                    "total_checks": 1,
                    "failed_checks": 1
                }
                results["summary"]["total_checks"] += 1
                results["summary"]["failed_checks"] += 1
        
        # 计算总体状态
        if results["summary"]["failed_checks"] == 0:
            results["overall_status"] = "healthy"
        elif results["summary"]["failed_checks"] < results["summary"]["total_checks"] / 2:
            results["overall_status"] = "degraded"
        else:
            results["overall_status"] = "unhealthy"
        
        logger.info(f"Validation completed: {results['overall_status']}")
        self.validation_results = results
        return results
    
    def _validate_directory_structure(self) -> Dict[str, Any]:
        """验证目录结构"""
        logger.info("Validating directory structure")
        
        required_dirs = [
            self.data_path,
            self.data_path / "mem0",
            self.data_path / "vector_store",
            self.data_path.parent / "logs",
            self.data_path.parent / "config"
        ]
        
        results = {
            "status": "passed",
            "total_checks": len(required_dirs),
            "passed_checks": 0,
            "failed_checks": 0,
            "warnings": 0,
            "details": {}
        }
        
        for dir_path in required_dirs:
            dir_name = str(dir_path.relative_to(self.data_path.parent))
            
            if dir_path.exists():
                if dir_path.is_dir():
                    results["details"][dir_name] = {
                        "status": "exists",
                        "readable": os.access(dir_path, os.R_OK),
                        "writable": os.access(dir_path, os.W_OK),
                        "size": self._get_directory_size(dir_path)
                    }
                    results["passed_checks"] += 1
                else:
                    results["details"][dir_name] = {
                        "status": "not_directory",
                        "error": "Path exists but is not a directory"
                    }
                    results["failed_checks"] += 1
            else:
                # 尝试创建目录
                try:
                    dir_path.mkdir(parents=True, exist_ok=True)
                    results["details"][dir_name] = {
                        "status": "created",
                        "readable": os.access(dir_path, os.R_OK),
                        "writable": os.access(dir_path, os.W_OK)
                    }
                    results["passed_checks"] += 1
                except Exception as e:
                    results["details"][dir_name] = {
                        "status": "missing",
                        "error": str(e)
                    }
                    results["failed_checks"] += 1
        
        if results["failed_checks"] > 0:
            results["status"] = "failed"
        
        return results
    
    def _validate_file_permissions(self) -> Dict[str, Any]:
        """验证文件权限"""
        logger.info("Validating file permissions")
        
        results = {
            "status": "passed",
            "total_checks": 0,
            "passed_checks": 0,
            "failed_checks": 0,  
            "warnings": 0,
            "details": {}
        }
        
        # 检查关键文件的权限
        critical_files = []
        if self.history_db_path.exists():
            critical_files.append(self.history_db_path)
        
        # 检查数据目录中的关键文件
        for pattern in ["*.db", "*.sqlite", "*.json"]:
            critical_files.extend(self.data_path.rglob(pattern))
        
        for file_path in critical_files:
            results["total_checks"] += 1
            file_name = str(file_path.relative_to(self.data_path.parent))
            
            try:
                stat_info = file_path.stat()
                perms = oct(stat_info.st_mode)[-3:]
                
                file_details = {
                    "permissions": perms,
                    "readable": os.access(file_path, os.R_OK),
                    "writable": os.access(file_path, os.W_OK),
                    "size": stat_info.st_size,
                    "owner": stat_info.st_uid,
                    "group": stat_info.st_gid
                }
                
                # 检查是否有必要的权限
                if file_details["readable"] and file_details["writable"]:
                    file_details["status"] = "ok"
                    results["passed_checks"] += 1
                else:
                    file_details["status"] = "permission_issue"
                    results["warnings"] += 1
                
                results["details"][file_name] = file_details
                
            except Exception as e:
                results["details"][file_name] = {
                    "status": "error",
                    "error": str(e)
                }
                results["failed_checks"] += 1
        
        if results["failed_checks"] > 0:
            results["status"] = "failed"
        elif results["warnings"] > 0:
            results["status"] = "warning"
        
        return results
    
    def _validate_database_integrity(self) -> Dict[str, Any]:
        """验证数据库完整性"""
        logger.info("Validating database integrity")
        
        results = {
            "status": "passed",
            "total_checks": 0,
            "passed_checks": 0,
            "failed_checks": 0,
            "warnings": 0,
            "details": {}
        }
        
        # 检查SQLite历史数据库
        if self.history_db_path.exists():
            results["total_checks"] += 1
            db_details = self._check_sqlite_database(self.history_db_path)
            results["details"]["history_database"] = db_details
            
            if db_details["status"] == "healthy":
                results["passed_checks"] += 1
            elif db_details["status"] == "warning":
                results["warnings"] += 1
            else:
                results["failed_checks"] += 1
        else:
            results["total_checks"] += 1
            results["details"]["history_database"] = {
                "status": "missing",
                "message": "History database file not found"
            }
            results["warnings"] += 1
        
        # 检查其他数据库文件
        for db_file in self.data_path.rglob("*.db"):
            if db_file != self.history_db_path:
                results["total_checks"] += 1
                db_name = str(db_file.relative_to(self.data_path))
                db_details = self._check_sqlite_database(db_file)
                results["details"][db_name] = db_details
                
                if db_details["status"] == "healthy":
                    results["passed_checks"] += 1
                elif db_details["status"] == "warning":
                    results["warnings"] += 1
                else:
                    results["failed_checks"] += 1
        
        if results["failed_checks"] > 0:
            results["status"] = "failed"
        elif results["warnings"] > 0:
            results["status"] = "warning"
        
        return results
    
    def _validate_data_consistency(self) -> Dict[str, Any]:
        """验证数据一致性"""
        logger.info("Validating data consistency")
        
        results = {
            "status": "passed",
            "total_checks": 0,
            "passed_checks": 0,
            "failed_checks": 0,
            "warnings": 0,
            "details": {}
        }
        
        # 检查历史数据库的数据一致性
        if self.history_db_path.exists():
            results["total_checks"] += 1
            consistency_check = self._check_data_consistency()
            results["details"]["data_consistency"] = consistency_check
            
            if consistency_check["status"] == "consistent":
                results["passed_checks"] += 1
            elif consistency_check["status"] == "minor_issues":
                results["warnings"] += 1
            else:
                results["failed_checks"] += 1
        
        # 检查配置文件一致性
        config_files = list(self.data_path.rglob("*.json")) + list(self.data_path.rglob("config*"))
        for config_file in config_files:
            results["total_checks"] += 1
            config_name = str(config_file.relative_to(self.data_path))
            
            try:
                if config_file.suffix == '.json':
                    with open(config_file, 'r') as f:
                        json.load(f)  # 验证JSON格式
                
                results["details"][f"config_{config_name}"] = {
                    "status": "valid",
                    "size": config_file.stat().st_size
                }
                results["passed_checks"] += 1
                
            except Exception as e:
                results["details"][f"config_{config_name}"] = {
                    "status": "invalid",
                    "error": str(e)
                }
                results["failed_checks"] += 1
        
        if results["failed_checks"] > 0:
            results["status"] = "failed"
        elif results["warnings"] > 0:
            results["status"] = "warning"
        
        return results
    
    def _validate_storage_health(self) -> Dict[str, Any]:
        """验证存储健康状况"""
        logger.info("Validating storage health")
        
        results = {
            "status": "passed",
            "total_checks": 3,
            "passed_checks": 0,
            "failed_checks": 0,
            "warnings": 0,
            "details": {}
        }
        
        # 检查磁盘空间
        try:
            stat = os.statvfs(self.data_path)
            total_space = stat.f_frsize * stat.f_blocks
            free_space = stat.f_frsize * stat.f_bavail
            used_space = total_space - free_space
            usage_percent = (used_space / total_space) * 100
            
            disk_info = {
                "total_gb": round(total_space / (1024**3), 2),
                "free_gb": round(free_space / (1024**3), 2),
                "used_gb": round(used_space / (1024**3), 2),
                "usage_percent": round(usage_percent, 2)
            }
            
            if usage_percent < 80:
                disk_info["status"] = "healthy"
                results["passed_checks"] += 1
            elif usage_percent < 90:
                disk_info["status"] = "warning"
                disk_info["message"] = "Disk usage is high"
                results["warnings"] += 1
            else:
                disk_info["status"] = "critical"
                disk_info["message"] = "Disk usage is critically high"
                results["failed_checks"] += 1
            
            results["details"]["disk_space"] = disk_info
            
        except Exception as e:
            results["details"]["disk_space"] = {
                "status": "error",
                "error": str(e)
            }
            results["failed_checks"] += 1
        
        # 检查数据目录大小
        try:
            data_size = self._get_directory_size(self.data_path)
            results["details"]["data_directory_size"] = {
                "status": "measured",
                "size_mb": round(data_size / (1024**2), 2),
                "size_gb": round(data_size / (1024**3), 2)
            }
            results["passed_checks"] += 1
            
        except Exception as e:
            results["details"]["data_directory_size"] = {
                "status": "error",
                "error": str(e)
            }
            results["failed_checks"] += 1
        
        # 检查I/O性能（简单的写入测试）
        try:
            test_file = self.data_path / ".io_test"
            start_time = datetime.now()
            
            with open(test_file, 'w') as f:
                f.write("test" * 1000)  # 写入4KB数据
            
            write_time = (datetime.now() - start_time).total_seconds()
            
            # 读取测试
            start_time = datetime.now()
            with open(test_file, 'r') as f:
                f.read()
            read_time = (datetime.now() - start_time).total_seconds()
            
            # 清理测试文件
            test_file.unlink()
            
            io_info = {
                "status": "measured",
                "write_time_ms": round(write_time * 1000, 2),
                "read_time_ms": round(read_time * 1000, 2)
            }
            
            if write_time < 0.1 and read_time < 0.05:
                io_info["performance"] = "good"
            elif write_time < 0.5 and read_time < 0.2:
                io_info["performance"] = "acceptable"
            else:
                io_info["performance"] = "slow"
                results["warnings"] += 1
            
            results["details"]["io_performance"] = io_info
            results["passed_checks"] += 1
            
        except Exception as e:
            results["details"]["io_performance"] = {
                "status": "error",
                "error": str(e)
            }
            results["failed_checks"] += 1
        
        if results["failed_checks"] > 0:
            results["status"] = "failed"
        elif results["warnings"] > 0:
            results["status"] = "warning"
        
        return results
    
    def _check_sqlite_database(self, db_path: Path) -> Dict[str, Any]:
        """检查SQLite数据库"""
        try:
            with sqlite3.connect(str(db_path), timeout=5) as conn:
                cursor = conn.cursor()
                
                # 检查数据库完整性
                cursor.execute("PRAGMA integrity_check")
                integrity_result = cursor.fetchone()[0]
                
                # 获取表信息
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                
                # 获取数据库统计
                stats = {}
                for table in tables:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    stats[table] = cursor.fetchone()[0]
                
                # 检查数据库大小
                cursor.execute("PRAGMA page_size")
                page_size = cursor.fetchone()[0]
                cursor.execute("PRAGMA page_count")
                page_count = cursor.fetchone()[0]
                db_size = page_size * page_count
                
                return {
                    "status": "healthy" if integrity_result == "ok" else "corrupted",
                    "integrity_check": integrity_result,
                    "tables": tables,
                    "table_stats": stats,
                    "size_bytes": db_size,
                    "size_mb": round(db_size / (1024**2), 2)
                }
                
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    def _check_data_consistency(self) -> Dict[str, Any]:
        """检查数据一致性"""
        try:
            with sqlite3.connect(str(self.history_db_path), timeout=5) as conn:
                cursor = conn.cursor()
                
                issues = []
                
                # 检查是否有重复记录
                cursor.execute("""
                    SELECT COUNT(*) as duplicate_count
                    FROM (
                        SELECT id, COUNT(*) as cnt 
                        FROM history 
                        GROUP BY id 
                        HAVING cnt > 1
                    )
                """)
                
                duplicate_count = cursor.fetchone()[0]
                if duplicate_count > 0:
                    issues.append(f"Found {duplicate_count} duplicate records")
                
                # 检查数据完整性
                cursor.execute("SELECT COUNT(*) FROM history WHERE id IS NULL OR id = ''")
                null_ids = cursor.fetchone()[0]
                if null_ids > 0:
                    issues.append(f"Found {null_ids} records with null/empty IDs")
                
                # 检查时间戳一致性
                cursor.execute("""
                    SELECT COUNT(*) FROM history 
                    WHERE created_at IS NULL OR created_at = ''
                """)
                null_timestamps = cursor.fetchone()[0]
                if null_timestamps > 0:
                    issues.append(f"Found {null_timestamps} records with null timestamps")
                
                if not issues:
                    return {
                        "status": "consistent",
                        "message": "No data consistency issues found"
                    }
                elif len(issues) <= 2:
                    return {
                        "status": "minor_issues",
                        "issues": issues
                    }
                else:
                    return {
                        "status": "major_issues",
                        "issues": issues
                    }
                    
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    def _get_directory_size(self, path: Path) -> int:
        """获取目录大小（字节）"""
        total_size = 0
        try:
            for file_path in path.rglob("*"):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
        except Exception:
            pass  # 忽略权限错误等
        return total_size
    
    def save_validation_report(self, output_path: Optional[str] = None) -> str:
        """保存验证报告"""
        if not self.validation_results:
            raise ValueError("No validation results available. Run validate_all() first.")
        
        if output_path is None:
            output_path = self.data_path.parent / "logs" / f"validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w') as f:
            json.dump(self.validation_results, f, indent=2)
        
        logger.info(f"Validation report saved to: {output_path}")
        return str(output_path)
    
    def get_health_status(self) -> str:
        """获取简单的健康状态"""
        if not self.validation_results:
            return "unknown"
        return self.validation_results.get("overall_status", "unknown")
    
    def fix_common_issues(self) -> Dict[str, Any]:
        """尝试修复常见问题"""
        logger.info("Attempting to fix common issues")
        
        fixes_applied = []
        
        # 创建缺失的目录
        required_dirs = [
            self.data_path,
            self.data_path / "mem0",
            self.data_path / "vector_store",
            self.data_path.parent / "logs",
            self.data_path.parent / "config"
        ]
        
        for dir_path in required_dirs:
            if not dir_path.exists():
                try:
                    dir_path.mkdir(parents=True, exist_ok=True)
                    fixes_applied.append(f"Created directory: {dir_path}")
                except Exception as e:
                    logger.error(f"Could not create directory {dir_path}: {e}")
        
        # 修复文件权限
        try:
            if self.history_db_path.exists():
                os.chmod(self.history_db_path, 0o664)
                fixes_applied.append(f"Fixed permissions for: {self.history_db_path}")
        except Exception as e:
            logger.error(f"Could not fix permissions: {e}")
        
        return {
            "fixes_applied": fixes_applied,
            "timestamp": datetime.now().isoformat()
        }


def verify_data_integrity_startup() -> bool:
    """启动时数据完整性验证"""
    try:
        validator = DataIntegrityValidator()
        results = validator.validate_all()
        
        status = results["overall_status"]
        logger.info(f"Data integrity check: {status}")
        
        if status == "unhealthy":
            logger.error("Critical data integrity issues found!")
            # 尝试修复
            fixes = validator.fix_common_issues()
            if fixes["fixes_applied"]:
                logger.info(f"Applied fixes: {fixes['fixes_applied']}")
                # 重新验证
                results = validator.validate_all()
                status = results["overall_status"]
        
        # 保存验证报告
        try:
            validator.save_validation_report()
        except Exception as e:
            logger.warning(f"Could not save validation report: {e}")
        
        return status in ["healthy", "degraded"]
        
    except Exception as e:
        logger.error(f"Data integrity validation failed: {e}")
        return False


if __name__ == "__main__":
    # 命令行使用
    import argparse
    
    parser = argparse.ArgumentParser(description="Mem0 Data Integrity Validator")
    parser.add_argument("--data-path", help="Data directory path")
    parser.add_argument("--history-db", help="History database path")
    parser.add_argument("--output", help="Output report path")
    parser.add_argument("--fix", action="store_true", help="Attempt to fix issues")
    parser.add_argument("--quiet", action="store_true", help="Quiet mode")
    
    args = parser.parse_args()
    
    if args.quiet:
        logging.getLogger().setLevel(logging.WARNING)
    
    validator = DataIntegrityValidator(args.data_path, args.history_db)
    results = validator.validate_all()
    
    if args.fix:
        fixes = validator.fix_common_issues()
        print(f"Applied {len(fixes['fixes_applied'])} fixes")
    
    # 输出结果
    print(f"Overall status: {results['overall_status']}")
    print(f"Total checks: {results['summary']['total_checks']}")
    print(f"Passed: {results['summary']['passed_checks']}")
    print(f"Failed: {results['summary']['failed_checks']}")
    print(f"Warnings: {results['summary']['warnings']}")
    
    if args.output:
        validator.save_validation_report(args.output)
    
    # 退出码
    sys.exit(0 if results['overall_status'] in ['healthy', 'degraded'] else 1)