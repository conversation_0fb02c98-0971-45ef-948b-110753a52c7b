#!/bin/bash
# =============================================================================
# 新配置验证脚本
# 验证所有优化配置是否正确应用
# =============================================================================

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[VERIFY]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[VERIFY]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[VERIFY]${NC} $1"
}

log_error() {
    echo -e "${RED}[VERIFY]${NC} $1"
}

echo "Mem0 配置验证"
echo "============"

# 检查文件替换
log_info "检查文件替换状态..."

if [ -f "entrypoint.sh.backup" ]; then
    log_success "✅ entrypoint.sh 已备份并替换"
else
    log_warning "⚠️ 未找到 entrypoint.sh.backup"
fi

if [ -f "Dockerfile.backup" ]; then
    log_success "✅ Dockerfile 已备份并替换"
else
    log_warning "⚠️ 未找到 Dockerfile.backup"  
fi

if [ -f "docker-compose.yaml.backup" ]; then
    log_success "✅ docker-compose.yaml 已备份并替换"
else
    log_warning "⚠️ 未找到 docker-compose.yaml.backup"
fi

# 检查脚本权限
log_info "检查脚本权限..."

if [ -x "entrypoint.sh" ]; then
    log_success "✅ entrypoint.sh 具有执行权限"
else
    log_error "❌ entrypoint.sh 没有执行权限"
fi

if [ -x "scripts/backup-data.sh" ]; then
    log_success "✅ backup-data.sh 具有执行权限"
else
    log_error "❌ backup-data.sh 没有执行权限"
fi

# 检查配置文件语法
log_info "检查 Docker Compose 配置语法..."

if docker compose config --quiet 2>/dev/null; then
    log_success "✅ Docker Compose 配置语法正确"
else
    log_error "❌ Docker Compose 配置语法错误"
fi

# 检查 Dockerfile 语法
log_info "检查 Dockerfile 语法..."

if docker build --dry-run -f Dockerfile .. >/dev/null 2>&1; then
    log_success "✅ Dockerfile 语法正确"
else
    log_warning "⚠️ Dockerfile 语法检查需要Docker daemon运行"
fi

# 检查环境变量文件
log_info "检查环境变量配置..."

if [ -f ".env" ]; then
    log_success "✅ .env 文件存在"
    
    # 检查关键环境变量
    if grep -q "OPENAI_API_KEY" .env; then
        log_info "  - 包含 OPENAI_API_KEY 配置"
    else
        log_warning "  ⚠️ 缺少 OPENAI_API_KEY 配置"
    fi
    
    if grep -q "PUID" .env; then
        log_info "  - 包含 PUID 配置"
    else
        log_info "  - 使用默认 PUID 配置"
    fi
else
    log_warning "⚠️ .env 文件不存在，将使用默认配置"
fi

# 检查目录结构
log_info "检查目录结构..."

required_dirs=("scripts" "data" "logs" "config")
for dir in "${required_dirs[@]}"; do
    if [ -d "$dir" ]; then
        log_success "✅ $dir/ 目录存在"
    else
        log_info "  创建 $dir/ 目录"
        mkdir -p "$dir"
    fi
done

# 检查新功能文件
log_info "检查新功能文件..."

new_features=(
    "data_validator.py:数据验证模块"
    "scripts/backup-data.sh:数据备份脚本" 
    "scripts/restore-data.sh:数据恢复脚本"
    "scripts/verify-data.sh:数据验证脚本"
    "scripts/fix-permissions.sh:权限修复脚本"
    "DEPLOYMENT_GUIDE.md:部署指南"
    "BACKUP_RESTORE.md:备份恢复指南"
    "TROUBLESHOOTING.md:故障排查指南"
)

for feature in "${new_features[@]}"; do
    file=$(echo "$feature" | cut -d: -f1)
    desc=$(echo "$feature" | cut -d: -f2)
    
    if [ -f "$file" ]; then
        log_success "✅ $desc ($file)"
    else
        log_error "❌ 缺少 $desc ($file)"
    fi
done

echo
log_info "配置摘要"
echo "========="
echo "📦 优化的文件："
echo "  - entrypoint.sh (300+ 行，完整启动检查)"
echo "  - Dockerfile (多阶段构建，镜像加速)"
echo "  - docker-compose.yaml (混合卷方案)"
echo ""
echo "🚀 新增功能："
echo "  - 完整的数据备份恢复系统"
echo "  - 自动权限修复机制"
echo "  - 数据完整性验证"
echo "  - 详细的部署和故障排查文档"
echo ""
echo "🔧 下一步："
echo "  1. 检查 .env 配置（特别是 OPENAI_API_KEY）"
echo "  2. 运行: docker compose up -d"
echo "  3. 查看启动日志: docker compose logs -f mem0-api"
echo "  4. 验证系统: ./scripts/verify-data.sh"

echo
log_success "🎉 配置验证完成！新的 Mem0 架构已就绪。"