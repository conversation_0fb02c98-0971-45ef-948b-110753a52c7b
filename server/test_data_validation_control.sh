#!/bin/bash

# 测试数据验证控制功能

echo "🧪 测试数据验证环境变量控制功能"
echo "========================================"

# 测试1: 禁用数据验证
echo "📋 测试1: ENABLE_DATA_VALIDATION=false (快速启动)"
echo "设置环境变量..."
export ENABLE_DATA_VALIDATION=false

echo "重启API服务..."
docker compose restart mem0-api > /dev/null 2>&1

echo "等待服务启动..."
sleep 8

echo "检查启动日志..."
if docker compose logs mem0-api --tail 5 | grep -q "Data integrity validation disabled"; then
    echo "✅ 测试1通过: 数据验证已禁用，启动快速"
else
    echo "❌ 测试1失败: 数据验证未正确禁用"
fi

echo ""

# 测试2: 启用数据验证
echo "📋 测试2: ENABLE_DATA_VALIDATION=true (完整验证)"
echo "设置环境变量..."
export ENABLE_DATA_VALIDATION=true

# 修改.env文件
sed -i 's/ENABLE_DATA_VALIDATION=false/ENABLE_DATA_VALIDATION=true/' .env

echo "重启API服务..."
docker compose restart mem0-api > /dev/null 2>&1

echo "等待服务启动和验证完成..."
sleep 15

echo "检查启动日志..."
if docker compose logs mem0-api --tail 10 | grep -q "Performing data integrity validation"; then
    echo "✅ 测试2通过: 数据验证已启用"
else
    echo "❌ 测试2失败: 数据验证未正确启用"
fi

# 恢复原设置
echo "恢复默认设置..."
sed -i 's/ENABLE_DATA_VALIDATION=true/ENABLE_DATA_VALIDATION=false/' .env
docker compose restart mem0-api > /dev/null 2>&1

echo ""
echo "🎯 测试完成！数据验证环境变量控制功能正常工作"
echo "使用方法:"
echo "  - 开发环境: ENABLE_DATA_VALIDATION=false (快速启动)"
echo "  - 生产环境: ENABLE_DATA_VALIDATION=true (完整验证)"