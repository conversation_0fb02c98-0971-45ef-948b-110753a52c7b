#!/bin/bash

# 清理没有分类标签的记忆数据

echo "🗑️ 清理没有分类标签的记忆数据"
echo "===================================="

# 获取所有用户列表
echo "📋 正在获取用户列表..."
users=$(curl -s "http://localhost:8000/v1/users?limit=100" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if 'users' in data:
        for user in data['users']:
            print(user['user_id'])
    else:
        print('default')  # 如果没有用户数据，使用默认用户
except:
    print('default')
")

if [ -z "$users" ]; then
    users="default test_user_categories test_user_auto test_user_auto_categorization final_test_user"
fi

total_deleted=0

for user_id in $users; do
    echo ""
    echo "🔍 检查用户: $user_id"
    
    # 获取该用户的记忆
    memories_response=$(curl -s "http://localhost:8000/v1/memories/?user_id=$user_id&limit=1000")
    
    # 处理响应并找出无分类标签的记忆
    memory_ids_to_delete=$(echo "$memories_response" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if isinstance(data, list):
        memories = data
    else:
        memories = data.get('memories', data.get('results', []))
    
    no_categories_ids = []
    with_categories_count = 0
    
    for item in memories:
        if not item.get('categories') or len(item.get('categories', [])) == 0:
            no_categories_ids.append(item['id'])
        else:
            with_categories_count += 1
    
    print(f'{len(no_categories_ids)} {with_categories_count}')
    for mem_id in no_categories_ids:
        print(mem_id)
except Exception as e:
    print('0 0')
")
    
    # 解析结果
    if [ -n "$memory_ids_to_delete" ]; then
        first_line=$(echo "$memory_ids_to_delete" | head -n 1)
        no_categories_count=$(echo "$first_line" | cut -d' ' -f1)
        with_categories_count=$(echo "$first_line" | cut -d' ' -f2)
        
        echo "   📊 有分类: $with_categories_count 个，无分类: $no_categories_count 个"
        
        if [ "$no_categories_count" != "0" ]; then
            echo "   🗑️  删除无分类标签的记忆..."
            memory_ids=$(echo "$memory_ids_to_delete" | tail -n +2)
            deleted_count=0
            
            for memory_id in $memory_ids; do
                if [ -n "$memory_id" ]; then
                    response=$(curl -s -X DELETE "http://localhost:8000/v1/memories/$memory_id")
                    if [[ $? -eq 0 ]]; then
                        ((deleted_count++))
                        ((total_deleted++))
                    fi
                fi
            done
            
            echo "   ✅ 已删除 $deleted_count 个无分类标签的记忆"
        else
            echo "   ✅ 该用户所有记忆都有分类标签"
        fi
    else
        echo "   ✅ 该用户没有记忆数据"
    fi
done

echo ""
echo "🎯 清理完成！"
echo "📊 总计删除了 $total_deleted 个无分类标签的记忆"
echo ""
echo "🔍 验证清理结果..."

# 验证清理结果
for user_id in $users; do
    verification=$(curl -s "http://localhost:8000/v1/memories/?user_id=$user_id&limit=1000" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if isinstance(data, list):
        memories = data
    else:
        memories = data.get('memories', data.get('results', []))
    
    no_categories = sum(1 for item in memories if not item.get('categories') or len(item.get('categories', [])) == 0)
    with_categories = sum(1 for item in memories if item.get('categories') and len(item.get('categories', [])) > 0)
    
    if no_categories == 0:
        print(f'✅ {user_id}: {with_categories}个记忆，全部有分类标签')
    else:
        print(f'⚠️ {user_id}: 仍有{no_categories}个记忆无分类标签，{with_categories}个有分类标签')
except:
    print(f'ℹ️ {user_id}: 无记忆数据')
")
    echo "$verification"
done