#!/bin/bash
# =============================================================================
# Mem0 数据恢复脚本
# 用于从备份中恢复所有持久化数据
# =============================================================================

set -e

# 配置
BACKUP_DIR="${BACKUP_DIR:-$(pwd)/backups}"
COMPOSE_FILE="${COMPOSE_FILE:-docker-compose.yaml}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
Mem0 Data Restore Script

Usage: $0 [BACKUP_NAME] [OPTIONS]

Arguments:
  BACKUP_NAME            Name of the backup to restore (e.g., mem0_backup_20241201_143000)

Options:
  -h, --help             Show this help message
  -d, --backup-dir DIR   Set backup directory (default: ./backups)
  -f, --compose-file FILE Set docker-compose file (default: docker-compose.yaml)
  -y, --yes              Skip confirmation prompts
  --dry-run              Show what would be restored without actually doing it

Examples:
  $0 mem0_backup_20241201_143000              # Restore specific backup
  $0 --list                                   # List available backups
  $0 mem0_backup_20241201_143000 --dry-run    # Preview restore process

EOF
}

# 列出可用备份
list_backups() {
    log_info "Available backups in $BACKUP_DIR:"
    if [ -d "$BACKUP_DIR" ]; then
        local backups=($(ls -1 "$BACKUP_DIR" | grep "^mem0_backup_" | sort -r))
        if [ ${#backups[@]} -eq 0 ]; then
            log_warning "No backups found"
        else
            for backup in "${backups[@]}"; do
                local backup_path="$BACKUP_DIR/$backup"
                local size=$(du -sh "$backup_path" 2>/dev/null | cut -f1 || echo "N/A")
                local date=$(stat -c %y "$backup_path" 2>/dev/null | cut -d' ' -f1 || echo "N/A")
                echo "  📦 $backup (Size: $size, Date: $date)"
            done
        fi
    else
        log_error "Backup directory not found: $BACKUP_DIR"
    fi
}

# 验证备份存在且完整
validate_backup() {
    local backup_name="$1"
    local backup_path="$BACKUP_DIR/$backup_name"
    
    if [ ! -d "$backup_path" ]; then
        log_error "Backup not found: $backup_path"
        return 1
    fi
    
    # 检查清单文件
    if [ ! -f "$backup_path/MANIFEST.txt" ]; then
        log_error "Backup manifest not found"
        return 1
    fi
    
    # 验证备份文件完整性
    log_info "Validating backup integrity..."
    local errors=0
    
    # 检查主机数据备份
    if [ -f "$backup_path/host_data.tar.gz" ]; then
        if ! tar -tzf "$backup_path/host_data.tar.gz" >/dev/null 2>&1; then
            log_error "Host data backup is corrupted"
            ((errors++))
        fi
    fi
    
    # 检查卷备份
    for volume_file in "$backup_path"/volume_*.tar.gz; do
        if [ -f "$volume_file" ]; then
            if ! tar -tzf "$volume_file" >/dev/null 2>&1; then
                local volume_name=$(basename "$volume_file" | sed 's/^volume_//' | sed 's/\.tar\.gz$//')
                log_error "Volume backup corrupted: $volume_name"
                ((errors++))
            fi
        fi
    done
    
    if [ $errors -eq 0 ]; then
        log_success "Backup validation passed"
        return 0
    else
        log_error "Backup validation failed with $errors errors"
        return 1
    fi
}

# 显示备份信息
show_backup_info() {
    local backup_name="$1"
    local backup_path="$BACKUP_DIR/$backup_name"
    
    log_info "Backup Information:"
    echo "  📍 Location: $backup_path"
    echo "  📊 Size: $(du -sh "$backup_path" | cut -f1)"
    echo "  📅 Created: $(stat -c %y "$backup_path" | cut -d'.' -f1)"
    
    if [ -f "$backup_path/MANIFEST.txt" ]; then
        echo "  📋 Manifest:"
        head -n 10 "$backup_path/MANIFEST.txt" | sed 's/^/    /'
    fi
    
    echo "  📦 Contents:"
    ls -la "$backup_path" | tail -n +2 | awk '{print "    " $9 " (" $5 " bytes)"}' 
}

# 停止服务
stop_services() {
    log_info "Stopping services..."
    if docker-compose -f "$COMPOSE_FILE" ps --services --filter status=running | grep -q .; then
        docker-compose -f "$COMPOSE_FILE" down
        log_success "Services stopped"
    else
        log_info "Services already stopped"
    fi
}

# 清理现有数据
cleanup_existing_data() {
    log_warning "Cleaning up existing data..."
    
    # 清理主机数据目录
    if [ -d "./data" ]; then
        log_info "Backing up existing data to ./data.backup.$(date +%s)"
        mv "./data" "./data.backup.$(date +%s)"
    fi
    
    # 清理Docker卷
    local volumes=($(docker-compose -f "$COMPOSE_FILE" config --volumes 2>/dev/null || echo ""))
    for volume in "${volumes[@]}"; do
        if docker volume inspect "$volume" >/dev/null 2>&1; then
            log_info "Removing Docker volume: $volume"
            docker volume rm "$volume" 2>/dev/null || true
        fi
    done
    
    log_success "Existing data cleanup completed"
}

# 恢复主机数据
restore_host_data() {
    local backup_name="$1"
    local backup_path="$BACKUP_DIR/$backup_name"
    
    if [ -f "$backup_path/host_data.tar.gz" ]; then
        log_info "Restoring host data..."
        mkdir -p "./data"
        tar -xzf "$backup_path/host_data.tar.gz" -C "./data"
        log_success "Host data restored"
    else
        log_warning "No host data backup found"
    fi
}

# 恢复Docker卷
restore_docker_volumes() {
    local backup_name="$1"
    local backup_path="$BACKUP_DIR/$backup_name"
    
    log_info "Restoring Docker volumes..."
    
    for volume_file in "$backup_path"/volume_*.tar.gz; do
        if [ -f "$volume_file" ]; then
            local volume_name=$(basename "$volume_file" | sed 's/^volume_//' | sed 's/\.tar\.gz$//')
            log_info "Restoring volume: $volume_name"
            
            # 创建卷
            docker volume create "$volume_name"
            
            # 恢复数据
            docker run --rm \
                -v "$volume_name":/target \
                -v "$backup_path":/backup:ro \
                alpine:latest \
                tar -xzf "/backup/$(basename "$volume_file")" -C /target
            
            log_success "Volume $volume_name restored"
        fi
    done
}

# 恢复配置文件
restore_configs() {
    local backup_name="$1"
    local backup_path="$BACKUP_DIR/$backup_name"
    local config_dir="$backup_path/configs"
    
    if [ -d "$config_dir" ]; then
        log_info "Restoring configuration files..."
        
        # 恢复Docker配置文件
        for config_file in docker-compose.yaml .env Dockerfile; do
            if [ -f "$config_dir/$config_file" ]; then
                log_info "Restoring $config_file"
                cp "$config_dir/$config_file" "./"
            fi
        done
        
        log_success "Configuration files restored"
    else
        log_warning "No configuration backup found"
    fi
}

# 启动服务
start_services() {
    log_info "Starting services..."
    docker-compose -f "$COMPOSE_FILE" up -d
    
    # 等待服务启动
    log_info "Waiting for services to be ready..."
    sleep 10
    
    # 检查健康状态
    local max_attempts=30
    local attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        if docker-compose -f "$COMPOSE_FILE" ps --services --filter status=running | grep -q mem0-api; then
            if timeout 5 python3 -c "import urllib.request; urllib.request.urlopen('http://localhost:8000/health', timeout=5)" >/dev/null 2>&1; then
                log_success "Services started and ready"
                return 0
            fi
        fi
        
        ((attempt++))
        log_info "Waiting for services... ($attempt/$max_attempts)"
        sleep 5
    done
    
    log_warning "Services started but health check failed"
    return 1
}

# 验证恢复结果
verify_restore() {
    local backup_name="$1"
    local backup_path="$BACKUP_DIR/$backup_name"
    
    log_info "Verifying restore results..."
    
    # 检查数据目录
    if [ ! -d "./data" ]; then
        log_error "Data directory not found after restore"
        return 1
    fi
    
    # 检查数据库状态
    if [ -f "$backup_path/database_stats.json" ]; then
        log_info "Comparing database statistics..."
        # 这里可以添加更详细的数据库验证逻辑
    fi
    
    # 检查服务状态
    if timeout 5 python3 -c "import urllib.request; urllib.request.urlopen('http://localhost:8000/health', timeout=5)" >/dev/null 2>&1; then
        log_success "API health check passed"
    else
        log_error "API health check failed"
        return 1
    fi
    
    log_success "Restore verification completed"
    return 0
}

# 主恢复函数
perform_restore() {
    local backup_name="$1"
    local dry_run="$2"
    
    if [ "$dry_run" = "true" ]; then
        log_info "DRY RUN: Showing what would be restored..."
        show_backup_info "$backup_name"
        return 0
    fi
    
    log_info "Starting restore process for: $backup_name"
    
    # 执行恢复步骤
    stop_services
    cleanup_existing_data
    restore_host_data "$backup_name"
    restore_docker_volumes "$backup_name"
    restore_configs "$backup_name"
    start_services
    
    # 验证恢复结果
    if verify_restore "$backup_name"; then
        log_success "✅ Restore completed successfully!"
        echo
        log_info "Services Status:"
        docker-compose -f "$COMPOSE_FILE" ps
        echo
        log_info "API Health: http://localhost:8000/health"
    else
        log_error "❌ Restore completed with errors"
        return 1
    fi
}

# 确认提示
confirm_restore() {
    local backup_name="$1"
    
    echo
    log_warning "⚠️  This will REPLACE all existing data with backup: $backup_name"
    log_warning "⚠️  Existing data will be backed up to ./data.backup.TIMESTAMP"
    echo
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Restore cancelled by user"
        exit 0
    fi
}

# 主函数
main() {
    local backup_name=""
    local dry_run="false"
    local skip_confirm="false"
    
    # 参数解析
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            --list)
                list_backups
                exit 0
                ;;
            -d|--backup-dir)
                BACKUP_DIR="$2"
                shift 2
                ;;
            -f|--compose-file)
                COMPOSE_FILE="$2"
                shift 2
                ;;
            -y|--yes)
                skip_confirm="true"
                shift
                ;;
            --dry-run)
                dry_run="true"
                shift
                ;;
            -*)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
            *)
                if [ -z "$backup_name" ]; then
                    backup_name="$1"
                else
                    log_error "Multiple backup names provided"
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    # 检查参数
    if [ -z "$backup_name" ]; then
        log_error "Backup name is required"
        echo
        list_backups
        exit 1
    fi
    
    # 验证备份
    if ! validate_backup "$backup_name"; then
        exit 1
    fi
    
    # 显示备份信息
    show_backup_info "$backup_name"
    
    # 确认恢复
    if [ "$skip_confirm" = "false" ] && [ "$dry_run" = "false" ]; then
        confirm_restore "$backup_name"
    fi
    
    # 执行恢复
    perform_restore "$backup_name" "$dry_run"
}

# 检查Docker
if ! docker info >/dev/null 2>&1; then
    log_error "Docker is not running or accessible"
    exit 1
fi

# 执行主函数
main "$@"