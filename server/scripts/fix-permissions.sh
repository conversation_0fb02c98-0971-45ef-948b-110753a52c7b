#!/bin/bash
# =============================================================================
# Mem0 文件权限修复脚本 (简化版本)
# 参考简化配置，避免复杂的权限处理逻辑
# =============================================================================

set -e

# 配置
DATA_DIR="${DATA_DIR:-./data}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助
show_help() {
    cat << EOF
Mem0 简化权限修复脚本

Usage: $0 [OPTIONS]

Options:
  -h, --help              Show this help message
  -d, --data-dir DIR      Set data directory (default: ./data)
  --dry-run               Show what would be changed without making changes

Examples:
  $0                      # Fix permissions with default settings
  $0 --dry-run            # Preview permission changes

Note: 
  这是简化版本的权限修复脚本，避免了复杂的用户切换逻辑。
  适用于简化的Docker配置。

EOF
}

# 检查权限问题
check_permissions() {
    log_info "Checking current permissions..."
    
    local issues=0
    
    # 检查数据目录
    if [ -d "$DATA_DIR" ]; then
        local dir_perms=$(stat -c "%a" "$DATA_DIR" 2>/dev/null || echo "unknown")
        echo "  Data directory ($DATA_DIR): $dir_perms"
        
        if [ ! -r "$DATA_DIR" ] || [ ! -w "$DATA_DIR" ]; then
            log_warning "Data directory has permission issues"
            ((issues++))
        fi
    else
        log_warning "Data directory does not exist: $DATA_DIR"
        ((issues++))
    fi
    
    # 检查重要文件
    local important_files=(
        "$DATA_DIR/mem0/config.json"
        "$DATA_DIR/mem0/history.db"
        "$DATA_DIR/history.db"
    )
    
    for file in "${important_files[@]}"; do
        if [ -f "$file" ]; then
            local file_perms=$(stat -c "%a" "$file" 2>/dev/null || echo "unknown")
            echo "  File ($file): $file_perms"
            
            if [ ! -r "$file" ] || [ ! -w "$file" ]; then
                log_warning "File has permission issues: $file"
                ((issues++))
            fi
        fi
    done
    
    # 检查脚本文件
    for script in scripts/*.sh; do
        if [ -f "$script" ]; then
            local script_perms=$(stat -c "%a" "$script" 2>/dev/null || echo "unknown")
            echo "  Script file ($script): $script_perms"
            
            if [ ! -x "$script" ]; then
                log_warning "Script is not executable: $script"
                ((issues++))
            fi
        fi
    done
    
    echo "  Total permission issues found: $issues"
    return $issues
}

# 简化的权限修复
fix_permissions() {
    local dry_run="$1"
    
    log_info "Fixing permissions (simplified approach)..."
    
    if [ "$dry_run" = "true" ]; then
        log_info "DRY RUN: Would fix the following permissions:"
    fi
    
    # 修复数据目录权限
    if [ -d "$DATA_DIR" ]; then
        echo "  Directories: chmod 755"
        if [ "$dry_run" != "true" ]; then
            find "$DATA_DIR" -type d -exec chmod 755 {} \; 2>/dev/null || log_warning "Could not fix directory permissions"
        fi
        
        echo "  Files: chmod 644"
        if [ "$dry_run" != "true" ]; then
            find "$DATA_DIR" -type f -exec chmod 644 {} \; 2>/dev/null || log_warning "Could not fix file permissions"
        fi
        
        # 特殊处理数据库文件 - 需要写入权限
        local db_files=(
            "$DATA_DIR/mem0/history.db"
            "$DATA_DIR/history.db"
            "$DATA_DIR/mem0/config.json"
        )
        
        for db_file in "${db_files[@]}"; do
            if [ -f "$db_file" ]; then
                echo "  Database/Config file: chmod 664 ($db_file)"
                if [ "$dry_run" != "true" ]; then
                    chmod 664 "$db_file" 2>/dev/null || log_warning "Could not fix database permissions: $db_file"
                fi
            fi
        done
    fi
    
    # 修复脚本权限
    if [ -d "scripts" ]; then
        echo "  Scripts: chmod +x"
        if [ "$dry_run" != "true" ]; then
            chmod +x scripts/*.sh 2>/dev/null || log_warning "Could not fix script permissions"
        fi
    fi
    
    log_success "Permission fix completed"
}

# 验证权限修复结果
verify_permissions() {
    log_info "Verifying permission fixes..."
    
    local issues=0
    
    # 检查数据目录
    if [ -d "$DATA_DIR" ]; then
        if [ -r "$DATA_DIR" ] && [ -w "$DATA_DIR" ]; then
            log_success "Data directory permissions: OK"
        else
            log_error "Data directory permissions: FAILED"
            ((issues++))
        fi
    fi
    
    # 检查重要文件
    local important_files=(
        "$DATA_DIR/mem0/config.json"
        "$DATA_DIR/mem0/history.db"
        "$DATA_DIR/history.db"
    )
    
    for file in "${important_files[@]}"; do
        if [ -f "$file" ]; then
            if [ -r "$file" ] && [ -w "$file" ]; then
                log_success "File permissions OK: $(basename "$file")"
            else
                log_error "File permissions FAILED: $file"
                ((issues++))
            fi
        fi
    done
    
    # 检查脚本权限
    local script_issues=0
    for script in scripts/*.sh; do
        if [ -f "$script" ] && [ ! -x "$script" ]; then
            ((script_issues++))
        fi
    done
    
    if [ $script_issues -eq 0 ]; then
        log_success "Script permissions: OK"
    else
        log_error "Script permissions: $script_issues scripts not executable"
        ((issues++))
    fi
    
    if [ $issues -eq 0 ]; then
        log_success "✅ All permission issues resolved"
        return 0
    else
        log_error "❌ $issues permission issues remain"
        return 1
    fi
}

# 主函数
main() {
    local dry_run=false
    
    # 参数解析
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -d|--data-dir)
                DATA_DIR="$2"
                shift 2
                ;;
            --dry-run)
                dry_run=true
                shift
                ;;
            *)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    echo "Mem0 简化权限修复"
    echo "================="
    echo "Data directory: $DATA_DIR"
    echo
    
    # 检查当前权限状态
    check_permissions
    echo
    
    # 执行权限修复
    fix_permissions "$dry_run"
    echo
    
    # 验证修复结果
    if [ "$dry_run" != "true" ]; then
        verify_permissions
    fi
    
    echo
    log_info "简化权限修复完成。"
    log_info "注意：此脚本适用于简化的Docker配置，不进行复杂的用户切换。"
}

# 执行主函数
main "$@"