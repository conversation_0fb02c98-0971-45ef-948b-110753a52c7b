#!/bin/bash
# =============================================================================
# Mem0 数据备份脚本
# 用于安全备份所有持久化数据，支持Docker卷和主机目录
# =============================================================================

set -e

# 配置
BACKUP_DIR="${BACKUP_DIR:-$(pwd)/backups}"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="mem0_backup_${TIMESTAMP}"
COMPOSE_FILE="${COMPOSE_FILE:-docker-compose.yaml}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否运行
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker is not running or accessible"
        exit 1
    fi
}

# 创建备份目录
create_backup_dir() {
    mkdir -p "${BACKUP_DIR}/${BACKUP_NAME}"
    log_info "Created backup directory: ${BACKUP_DIR}/${BACKUP_NAME}"
}

# 备份主机数据目录
backup_host_data() {
    local data_dir="./data"
    if [ -d "$data_dir" ]; then
        log_info "Backing up host data directory..."
        tar -czf "${BACKUP_DIR}/${BACKUP_NAME}/host_data.tar.gz" -C "$data_dir" .
        log_success "Host data backup completed"
    else
        log_warning "Host data directory not found: $data_dir"
    fi
}

# 备份Docker卷
backup_docker_volumes() {
    log_info "Backing up Docker volumes..."
    
    # 获取所有相关的Docker卷
    local volumes=($(docker-compose -f "$COMPOSE_FILE" config --volumes 2>/dev/null || echo ""))
    
    if [ ${#volumes[@]} -eq 0 ]; then
        log_warning "No Docker volumes found in compose file"
        return
    fi
    
    for volume in "${volumes[@]}"; do
        if docker volume inspect "$volume" >/dev/null 2>&1; then
            log_info "Backing up volume: $volume"
            docker run --rm \
                -v "$volume":/source:ro \
                -v "${BACKUP_DIR}/${BACKUP_NAME}":/backup \
                alpine:latest \
                tar -czf "/backup/volume_${volume}.tar.gz" -C /source .
            log_success "Volume $volume backup completed"
        else
            log_warning "Volume $volume not found"
        fi
    done
}

# 备份数据库状态
backup_database_state() {
    log_info "Backing up database states..."
    
    # 检查服务是否运行
    if docker-compose -f "$COMPOSE_FILE" ps --services --filter status=running | grep -q mem0-api; then
        # 备份SQLite数据库
        docker-compose -f "$COMPOSE_FILE" exec -T mem0-api \
            python -c "
import sqlite3
import os
import json
from datetime import datetime

try:
    # 检查历史数据库
    if os.path.exists('/app/data/history.db'):
        conn = sqlite3.connect('/app/data/history.db')
        cursor = conn.cursor()
        
        # 获取表信息
        cursor.execute(\"SELECT name FROM sqlite_master WHERE type='table'\")
        tables = cursor.fetchall()
        
        # 统计记录数
        stats = {'timestamp': datetime.now().isoformat(), 'tables': {}}
        for table in tables:
            table_name = table[0]
            cursor.execute(f'SELECT COUNT(*) FROM {table_name}')
            count = cursor.fetchone()[0]
            stats['tables'][table_name] = count
        
        # 保存统计信息
        print(json.dumps(stats, indent=2))
        conn.close()
    else:
        print('{\"error\": \"Database file not found\"}')
except Exception as e:
    print(f'{\"error\": \"{str(e)}\"}')
" > "${BACKUP_DIR}/${BACKUP_NAME}/database_stats.json" 2>/dev/null || true
        
        log_success "Database state backup completed"
    else
        log_warning "Services not running, skipping database state backup"
    fi
}

# 备份配置文件
backup_configs() {
    log_info "Backing up configuration files..."
    
    # 创建配置备份目录
    mkdir -p "${BACKUP_DIR}/${BACKUP_NAME}/configs"
    
    # 备份Docker配置
    if [ -f "$COMPOSE_FILE" ]; then
        cp "$COMPOSE_FILE" "${BACKUP_DIR}/${BACKUP_NAME}/configs/"
    fi
    
    # 备份环境变量文件
    if [ -f ".env" ]; then
        cp ".env" "${BACKUP_DIR}/${BACKUP_NAME}/configs/"
    fi
    
    # 备份Dockerfile
    if [ -f "Dockerfile" ]; then
        cp "Dockerfile" "${BACKUP_DIR}/${BACKUP_NAME}/configs/"
    fi
    
    # 备份应用配置
    if [ -f "main.py" ]; then
        cp "main.py" "${BACKUP_DIR}/${BACKUP_NAME}/configs/main.py.backup"
    fi
    
    log_success "Configuration backup completed"
}

# 生成备份清单
generate_manifest() {
    log_info "Generating backup manifest..."
    
    cat > "${BACKUP_DIR}/${BACKUP_NAME}/MANIFEST.txt" << EOF
Mem0 Data Backup Manifest
========================
Backup Name: ${BACKUP_NAME}
Timestamp: ${TIMESTAMP}
Created: $(date)
Host: $(hostname)
User: $(whoami)
Working Directory: $(pwd)

Backup Contents:
$(ls -la "${BACKUP_DIR}/${BACKUP_NAME}")

System Info:
- Docker Version: $(docker --version 2>/dev/null || echo "N/A")
- Docker Compose Version: $(docker-compose --version 2>/dev/null || echo "N/A")
- OS: $(uname -a)

Notes:
- Use restore-data.sh to restore this backup
- Verify data integrity after restoration
- Keep this manifest with the backup files
EOF

    log_success "Backup manifest generated"
}

# 验证备份完整性
verify_backup() {
    log_info "Verifying backup integrity..."
    
    local backup_path="${BACKUP_DIR}/${BACKUP_NAME}"
    local errors=0
    
    # 检查必要文件
    if [ ! -f "$backup_path/MANIFEST.txt" ]; then
        log_error "Manifest file missing"
        ((errors++))
    fi
    
    # 检查备份文件
    if [ -f "$backup_path/host_data.tar.gz" ]; then
        if ! tar -tzf "$backup_path/host_data.tar.gz" >/dev/null 2>&1; then
            log_error "Host data backup is corrupted"
            ((errors++))
        fi
    fi
    
    # 检查卷备份文件
    for volume_file in "$backup_path"/volume_*.tar.gz; do
        if [ -f "$volume_file" ]; then
            if ! tar -tzf "$volume_file" >/dev/null 2>&1; then
                log_error "Volume backup is corrupted: $(basename "$volume_file")"
                ((errors++))
            fi
        fi
    done
    
    if [ $errors -eq 0 ]; then
        log_success "Backup verification completed successfully"
        return 0
    else
        log_error "Backup verification failed with $errors errors"
        return 1
    fi
}

# 清理旧备份
cleanup_old_backups() {
    local keep_days=${BACKUP_RETENTION_DAYS:-7}
    log_info "Cleaning up backups older than $keep_days days..."
    
    find "$BACKUP_DIR" -type d -name "mem0_backup_*" -mtime +$keep_days -exec rm -rf {} \; 2>/dev/null || true
    log_success "Old backup cleanup completed"
}

# 主函数
main() {
    log_info "Starting Mem0 data backup process..."
    log_info "Backup will be saved to: ${BACKUP_DIR}/${BACKUP_NAME}"
    
    # 执行备份步骤
    check_docker
    create_backup_dir
    backup_host_data
    backup_docker_volumes
    backup_database_state
    backup_configs
    generate_manifest
    
    # 验证备份
    if verify_backup; then
        log_success "✅ Backup completed successfully!"
        log_info "Backup location: ${BACKUP_DIR}/${BACKUP_NAME}"
        log_info "Total size: $(du -sh "${BACKUP_DIR}/${BACKUP_NAME}" | cut -f1)"
    else
        log_error "❌ Backup completed with errors"
        exit 1
    fi
    
    # 清理旧备份
    cleanup_old_backups
    
    echo
    log_info "To restore this backup, run:"
    log_info "  ./scripts/restore-data.sh ${BACKUP_NAME}"
}

# 显示帮助信息
show_help() {
    cat << EOF
Mem0 Data Backup Script

Usage: $0 [OPTIONS]

Options:
  -h, --help              Show this help message
  -d, --backup-dir DIR    Set backup directory (default: ./backups)
  -f, --compose-file FILE Set docker-compose file (default: docker-compose.yaml)
  -r, --retention DAYS    Set backup retention days (default: 7)

Environment Variables:
  BACKUP_DIR              Backup directory path
  COMPOSE_FILE            Docker compose file path
  BACKUP_RETENTION_DAYS   Number of days to keep backups

Examples:
  $0                      # Basic backup
  $0 -d /backup/mem0      # Custom backup directory
  $0 -f docker-compose.prod.yaml  # Custom compose file

EOF
}

# 参数解析
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -d|--backup-dir)
            BACKUP_DIR="$2"
            shift 2
            ;;
        -f|--compose-file)
            COMPOSE_FILE="$2"
            shift 2
            ;;
        -r|--retention)
            BACKUP_RETENTION_DAYS="$2"
            shift 2
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行主函数
main