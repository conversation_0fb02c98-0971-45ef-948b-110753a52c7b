#!/bin/bash
# =============================================================================
# Mem0 数据验证脚本
# 用于验证数据完整性和系统健康状态
# =============================================================================

set -e

# 配置
COMPOSE_FILE="${COMPOSE_FILE:-docker-compose.yaml}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 验证计数器
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
WARNING_CHECKS=0

# 检查函数
check() {
    local description="$1"
    local command="$2"
    local expected_result="$3"
    
    ((TOTAL_CHECKS++))
    echo -n "  Checking $description... "
    
    if eval "$command" >/dev/null 2>&1; then
        echo -e "${GREEN}✓${NC}"
        ((PASSED_CHECKS++))
        return 0
    else
        echo -e "${RED}✗${NC}"
        ((FAILED_CHECKS++))
        return 1
    fi
}

# 警告检查函数
check_warning() {
    local description="$1"
    local command="$2"
    
    ((TOTAL_CHECKS++))
    echo -n "  Checking $description... "
    
    if eval "$command" >/dev/null 2>&1; then
        echo -e "${GREEN}✓${NC}"
        ((PASSED_CHECKS++))
        return 0
    else
        echo -e "${YELLOW}⚠${NC}"
        ((WARNING_CHECKS++))
        return 1
    fi
}

# 验证Docker环境
verify_docker_environment() {
    log_info "Verifying Docker environment..."
    
    check "Docker daemon" "docker info"
    check "Docker Compose" "docker-compose --version"
    check "Compose file exists" "test -f '$COMPOSE_FILE'"
    
    # 检查Docker卷
    local volumes=($(docker-compose -f "$COMPOSE_FILE" config --volumes 2>/dev/null || echo ""))
    for volume in "${volumes[@]}"; do
        check_warning "Docker volume: $volume" "docker volume inspect '$volume'"
    done
}

# 验证服务状态
verify_services() {
    log_info "Verifying service status..."
    
    # 检查服务是否运行
    local services=("mem0-api" "mem0-qdrant" "mem0-neo4j")
    for service in "${services[@]}"; do
        check "Service running: $service" "docker ps --filter name=$service --filter status=running --quiet | grep -q ."
    done
    
    # 检查服务健康状态
    check_warning "Mem0 API health" "timeout 5 python3 -c 'import urllib.request; urllib.request.urlopen(\"http://localhost:8000/health\", timeout=5)'"
    check_warning "Qdrant health" "timeout 5 bash -c 'echo > /dev/tcp/localhost/6333'"
    check_warning "Neo4j health" "timeout 5 bash -c 'echo > /dev/tcp/localhost/7474'"
}

# 验证数据完整性
verify_data_integrity() {
    log_info "Verifying data integrity..."
    
    # 检查主机数据目录
    check "Data directory exists" "test -d './data'"
    check "Mem0 data directory" "test -d './data/mem0'"
    check_warning "History database exists" "test -f './data/mem0/history.db'"
    check_warning "Vector store directory" "test -d './data/vector_store'"
    
    # 检查数据库连接和数据
    if docker-compose -f "$COMPOSE_FILE" ps --services --filter status=running | grep -q mem0-api; then
        # SQLite数据库检查
        local db_check_result=$(docker-compose -f "$COMPOSE_FILE" exec -T mem0-api python -c "
import sqlite3
import os
import sys

try:
    if os.path.exists('/app/data/history.db'):
        conn = sqlite3.connect('/app/data/history.db', timeout=5)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute(\"SELECT name FROM sqlite_master WHERE type='table'\")
        tables = cursor.fetchall()
        
        if not tables:
            print('NO_TABLES')
            sys.exit(1)
        
        # 检查是否有数据
        total_records = 0
        for table in tables:
            cursor.execute(f'SELECT COUNT(*) FROM {table[0]}')
            count = cursor.fetchone()[0]
            total_records += count
        
        print(f'OK:{len(tables)}:{total_records}')
        conn.close()
    else:
        print('NO_DB')
        sys.exit(1)
except Exception as e:
    print(f'ERROR:{str(e)}')
    sys.exit(1)
" 2>/dev/null || echo "ERROR:Connection failed")
        
        case "$db_check_result" in
            OK:*)
                tables=$(echo "$db_check_result" | cut -d: -f2)
                records=$(echo "$db_check_result" | cut -d: -f3)
                echo -e "  SQLite database: ${GREEN}✓${NC} ($tables tables, $records records)"
                ((PASSED_CHECKS++))
                ;;
            NO_TABLES)
                echo -e "  SQLite database: ${YELLOW}⚠${NC} (no tables found)"
                ((WARNING_CHECKS++))
                ;;
            NO_DB)
                echo -e "  SQLite database: ${RED}✗${NC} (database file not found)"
                ((FAILED_CHECKS++))
                ;;
            ERROR:*)
                error_msg=$(echo "$db_check_result" | cut -d: -f2-)
                echo -e "  SQLite database: ${RED}✗${NC} ($error_msg)"
                ((FAILED_CHECKS++))
                ;;
        esac
        ((TOTAL_CHECKS++))
        
        # 检查API功能
        local api_test_result=$(timeout 10 python3 -c "
import urllib.request
import json
try:
    response = urllib.request.urlopen('http://localhost:8000/v1/stats', timeout=5)
    data = json.loads(response.read().decode())
    if 'total_memories' in data:
        print(f'OK:{data[\"total_memories\"]}')
    else:
        print('INVALID_RESPONSE')
except Exception as e:
    print(f'ERROR:{str(e)}')
" 2>/dev/null || echo "CONNECTION_ERROR")
        
        case "$api_test_result" in
            OK:*)
                memories=$(echo "$api_test_result" | cut -d: -f2)
                echo -e "  API functionality: ${GREEN}✓${NC} ($memories memories)"
                ((PASSED_CHECKS++))
                ;;
            *)
                echo -e "  API functionality: ${RED}✗${NC} ($api_test_result)"
                ((FAILED_CHECKS++))
                ;;
        esac
        ((TOTAL_CHECKS++))
    else
        log_warning "Services not running, skipping database checks"
    fi
}

# 验证网络连接
verify_network() {
    log_info "Verifying network connectivity..."
    
    # 检查端口是否开放
    local ports=("8000:API" "6333:Qdrant" "7474:Neo4j HTTP" "7687:Neo4j Bolt")
    for port_info in "${ports[@]}"; do
        local port=$(echo "$port_info" | cut -d: -f1)
        local service=$(echo "$port_info" | cut -d: -f2)
        check_warning "Port $port ($service)" "nc -z localhost $port"
    done
    
    # 检查Docker网络
    check "Docker network exists" "docker network inspect mem0-network"
}

# 验证文件权限
verify_permissions() {
    log_info "Verifying file permissions..."
    
    # 检查数据目录权限
    if [ -d "./data" ]; then
        check "Data directory readable" "test -r './data'"
        check "Data directory writable" "test -w './data'"
        
        # 检查数据库文件权限
        if [ -f "./data/mem0/history.db" ]; then
            check "Database file readable" "test -r './data/mem0/history.db'"
            check "Database file writable" "test -w './data/mem0/history.db'"
        fi
    fi
    
    # 检查脚本权限
    check "Backup script executable" "test -x './scripts/backup-data.sh'"
    check "Restore script executable" "test -x './scripts/restore-data.sh'"
}

# 验证系统资源
verify_system_resources() {
    log_info "Verifying system resources..."
    
    # 检查磁盘空间
    local data_size=$(du -sh ./data 2>/dev/null | cut -f1 || echo "0")
    local available_space=$(df . | tail -1 | awk '{print $4}')
    local available_gb=$((available_space / 1024 / 1024))
    
    echo "  Data directory size: $data_size"
    echo "  Available disk space: ${available_gb}GB"
    
    if [ $available_gb -lt 1 ]; then
        echo -e "  Disk space: ${RED}✗${NC} (less than 1GB available)"
        ((FAILED_CHECKS++))
    else
        echo -e "  Disk space: ${GREEN}✓${NC}"
        ((PASSED_CHECKS++))
    fi
    ((TOTAL_CHECKS++))
    
    # 检查内存使用
    if command -v free >/dev/null; then
        local available_mem=$(free -m | awk 'NR==2{printf "%.0f", $7}')
        echo "  Available memory: ${available_mem}MB"
        
        if [ $available_mem -lt 512 ]; then
            echo -e "  Memory: ${YELLOW}⚠${NC} (less than 512MB available)"
            ((WARNING_CHECKS++))
        else
            echo -e "  Memory: ${GREEN}✓${NC}"
            ((PASSED_CHECKS++))
        fi
        ((TOTAL_CHECKS++))
    fi
}

# 性能基准测试
run_performance_benchmark() {
    log_info "Running performance benchmark..."
    
    if ! docker-compose -f "$COMPOSE_FILE" ps --services --filter status=running | grep -q mem0-api; then
        log_warning "Services not running, skipping performance tests"
        return
    fi
    
    # API响应时间测试
    local start_time=$(date +%s.%N)
    if timeout 5 python3 -c "import urllib.request; urllib.request.urlopen('http://localhost:8000/health', timeout=5)" >/dev/null 2>&1; then
        local end_time=$(date +%s.%N)
        local response_time=$(echo "$end_time - $start_time" | bc 2>/dev/null || echo "0")
        local response_ms=$(echo "$response_time * 1000" | bc 2>/dev/null || echo "0")
    else
        local response_ms="timeout"
    fi
    
    echo "  API response time: ${response_ms}ms"
    
    if [ "$response_ms" = "timeout" ]; then
        echo -e "  API performance: ${RED}✗${NC} (timeout)"
        ((FAILED_CHECKS++))
    elif (( $(echo "$response_time > 2.0" | bc -l 2>/dev/null || echo 0) )); then
        echo -e "  API performance: ${YELLOW}⚠${NC} (slow response: ${response_ms}ms)"
        ((WARNING_CHECKS++))
    else
        echo -e "  API performance: ${GREEN}✓${NC} (${response_ms}ms)"
        ((PASSED_CHECKS++))
    fi
    ((TOTAL_CHECKS++))
}

# 生成验证报告
generate_report() {
    echo
    log_info "Verification Summary"
    echo "===================="
    echo "  Total checks: $TOTAL_CHECKS"
    echo -e "  Passed: ${GREEN}$PASSED_CHECKS${NC}"
    echo -e "  Failed: ${RED}$FAILED_CHECKS${NC}"
    echo -e "  Warnings: ${YELLOW}$WARNING_CHECKS${NC}"
    echo
    
    local success_rate=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))
    echo "  Success rate: $success_rate%"
    
    if [ $FAILED_CHECKS -eq 0 ]; then
        if [ $WARNING_CHECKS -eq 0 ]; then
            log_success "✅ All checks passed! System is healthy."
        else
            log_warning "⚠️ All critical checks passed, but there are $WARNING_CHECKS warnings."
        fi
        return 0
    else
        log_error "❌ $FAILED_CHECKS critical checks failed. System may not be functioning properly."
        return 1
    fi
}

# 显示帮助
show_help() {
    cat << EOF
Mem0 Data Verification Script

Usage: $0 [OPTIONS]

Options:
  -h, --help              Show this help message
  -f, --compose-file FILE Set docker-compose file (default: docker-compose.yaml)  
  --quick                 Run only basic checks (skip performance tests)
  --detailed              Include detailed diagnostics
  --fix-permissions       Attempt to fix file permission issues

Examples:
  $0                      # Full verification
  $0 --quick              # Quick health check
  $0 --detailed           # Detailed diagnostics

EOF
}

# 修复权限
fix_permissions() {
    log_info "Attempting to fix file permissions..."
    
    if [ -d "./data" ]; then
        chmod -R 755 ./data 2>/dev/null || log_warning "Could not fix data directory permissions"
        
        if [ -f "./data/mem0/history.db" ]; then
            chmod 664 "./data/mem0/history.db" 2>/dev/null || log_warning "Could not fix database file permissions"
        fi
    fi
    
    # 修复脚本权限
    chmod +x ./scripts/*.sh 2>/dev/null || log_warning "Could not fix script permissions"
    
    log_success "Permission fix attempt completed"
}

# 主函数
main() {
    local quick_mode=false
    local detailed_mode=false
    local fix_perms=false
    
    # 参数解析
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -f|--compose-file)
                COMPOSE_FILE="$2"
                shift 2
                ;;
            --quick)
                quick_mode=true
                shift
                ;;
            --detailed)
                detailed_mode=true
                shift
                ;;
            --fix-permissions)
                fix_perms=true
                shift
                ;;
            *)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    echo "Mem0 System Verification"
    echo "======================="
    echo "Timestamp: $(date)"
    echo "Compose file: $COMPOSE_FILE"
    echo
    
    # 修复权限（如果请求）
    if [ "$fix_perms" = true ]; then
        fix_permissions
        echo
    fi
    
    # 执行验证检查
    verify_docker_environment
    verify_services
    verify_data_integrity
    verify_network
    verify_permissions
    verify_system_resources
    
    # 可选的性能测试
    if [ "$quick_mode" = false ]; then
        run_performance_benchmark
    fi
    
    # 生成报告
    generate_report
}

# 检查依赖
if ! command -v docker >/dev/null; then
    log_error "Docker is not installed or not in PATH"
    exit 1
fi

if ! command -v docker-compose >/dev/null; then
    log_error "Docker Compose is not installed or not in PATH"
    exit 1
fi

# 执行主函数
main "$@"