identity:
  author: mocha
  name: mem0_opensource
  label:
    en_US: Mem0 Memory
    zh_Hans: Mem0记忆
    pt_BR: Mem0 Memória
  description:
    en_US: AI memory management for conversations
    zh_Hans: AI对话记忆管理
    pt_BR: Gerenciamento de memória IA
  icon: mem0.png

credentials_for_provider:
  mem0_api_key:
    type: secret-input
    required: true
    label:
      en_US: Mem0 API key
      zh_Hans: Mem0 API Key
      pt_BR: Mem0 API key
    placeholder:
      en_US: "Please input your Mem0 API key"
      zh_Hans: "请输入你的 Mem0 API Key"
      pt_BR: "Please input your Mem0 API key"
    help:
      en_US: "Please visit the Mem0 AI Dashboard to register an account and apply for an API key"
      zh_<PERSON>: "请访问 Mem0 AI 仪表板注册账号并获取 API Key"
      pt_BR: "Please visit the Mem0 AI Dashboard to register an account and apply for an API key"
    url: https://app.mem0.ai/dashboard/api-keys
  mem0_api_url:
    type: text-input
    required: false
    default: "http://localhost:8000"
    label:
      en_US: Mem0 API URL
      zh_Hans: Mem0 API 地址
      pt_BR: Mem0 API URL
    placeholder:
      en_US: "http://localhost:8000 or https://api.mem0.ai"
      zh_Hans: "http://localhost:8000 或 https://api.mem0.ai"
      pt_BR: "http://localhost:8000 ou https://api.mem0.ai"
    help:
      en_US: "API URL for Mem0 service (use http://localhost:8000 for local deployment or https://api.mem0.ai for cloud)"
      zh_Hans: "Mem0服务的API地址（本地部署使用 http://localhost:8000，云端使用 https://api.mem0.ai）"
      pt_BR: "URL da API para o serviço Mem0 (use http://localhost:8000 para implantação local ou https://api.mem0.ai para nuvem)"

tools:
  # 📝 Memory Creation & Management
  - tools/add_memory.yaml              # 💾 Basic memory storage
  - tools/multimodal_add.yaml          # 🎨 Multimodal content support

  # 🔍 Memory Retrieval & Search
  - tools/retrieve_memory.yaml         # 🔍 Basic memory search
  - tools/retrieve_memory_v2.yaml      # 🔍⚡ Advanced search with filters
  - tools/get_memories_v2.yaml         # 📋 Batch memory retrieval

  # ⚙️ Memory Operations
  - tools/update_memory.yaml           # ✏️ Memory content updates
  - tools/delete_memory.yaml           # 🗑️ Memory removal
  - tools/memory_history.yaml          # 📜 Version tracking

  # ✅ 8个核心工具：完整的记忆管理生态系统
extra:
  python:
    source: provider/mem0.py 