# Mem0 Open Source Plugin for Dify

*For Chinese documentation, see [README_CN.md](README_CN.md)*

## 🚀 Overview

The **Mem0 Open Source Plugin** is a comprehensive memory management solution for Dify, providing advanced conversation storage and AI-powered memory extraction capabilities. This plugin enables intelligent memory management with support for multimodal content, semantic search, and sophisticated memory operations.

**Author:** mocha (Secondary Development)
**Original Author:** yevanchen
**Version:** 0.2.0
**Type:** Memory Management Plugin

## 🙏 Acknowledgments

This plugin is based on the original work by **yevanchen**. We extend our sincere gratitude for the foundational implementation and innovative approach to memory management in Dify. This secondary development builds upon that excellent foundation to provide enhanced features and improved user experience.

**Original Contributions:**
- Basic memory add and search functionality
- Initial Mem0 API integration foundation
- Core plugin structure for Dify

**Secondary Development Enhancements:**
- **Custom Local Deployment Support**: Added configurable API URL for self-hosted Mem0 instances
- **Complete Tool Suite**: Expanded from basic add/search to 8 comprehensive tools
- **Advanced API Integration**: Full V1 and V2 API support with multimodal capabilities
- **Enhanced Visual Design**: Emoji icons and improved UI/UX organization
- **Complete Internationalization**: 4-language support (EN/CN/JP/PT)
- **Comprehensive Documentation**: Bilingual guides and developer documentation
- **Performance Optimization**: Enhanced error handling and response processing


### ✨ Key Features

#### 📝 **Memory Creation & Management**
- **💾 Basic Memory Storage**: Store conversation memories with AI-powered inference
- **🎨 Multimodal Support**: Handle text, images, documents, and PDFs seamlessly
- **🧠 AI Inference**: Extract meaningful insights and context from conversations
- **🔗 Relationship Mapping**: Automatic discovery of memory relationships

#### 🔍 **Advanced Search & Retrieval**
- **🔍 Semantic Search**: Find memories using natural language queries
- **⚡ Advanced Filtering**: Similarity thresholds, metadata filters, and pagination
- **📋 Batch Operations**: Retrieve and manage multiple memories efficiently
- **🎯 Contextual Matching**: Intelligent memory matching based on conversation context

#### ⚙️ **Memory Operations**
- **✏️ Memory Updates**: Modify and enhance existing memories
- **🗑️ Safe Deletion**: Remove unwanted or outdated memories
- **📜 Version History**: Track all changes and modifications over time
- **🔄 Memory Lifecycle**: Complete memory management from creation to deletion

#### 🛡️ **Security & Privacy**
- **🔐 User Isolation**: Complete data separation between users
- **🔑 API Authentication**: Secure access with API key management
- **🌐 Flexible Deployment**: Support for both self-hosted and cloud solutions
- **📊 Audit Logging**: Comprehensive activity tracking

### 🛠️ Installation

1. **Download** the plugin package from the releases
2. **Upload** to your Dify instance via the plugin management interface
3. **Configure** your Mem0 API credentials:
   - **API Key**: Your Mem0 authentication key
   - **API URL**: Mem0 server endpoint (default: `http://localhost:8000`)
4. **Activate** the plugin and start using memory management tools

### 🔧 Configuration

#### Required Settings
- **Mem0 API Key**: Authentication key for Mem0 service
- **Mem0 API URL**: Server endpoint (self-hosted or cloud)

#### Optional Settings
- **Default Inference Mode**: Enable/disable AI inference by default
- **Memory Retention Policy**: Configure automatic cleanup rules
- **Search Preferences**: Set default similarity thresholds

### 📚 Available Tools

| Tool | Description | Use Case |
|------|-------------|----------|
| 💾 **Add Memory** | Store conversation memories with AI inference | Basic memory creation |
| 🎨 **Multimodal Memory** | Process images, documents, and PDFs | Rich content storage |
| 🔍 **Retrieve Memory** | Search memories using keywords and semantics | Basic memory retrieval |
| 🔍⚡ **Advanced Search** | Advanced filtering with similarity thresholds | Precise memory finding |
| 📋 **Batch Retrieval** | Get multiple memories with sorting and pagination | Memory management |
| ✏️ **Update Memory** | Modify existing memory content | Memory maintenance |
| 🗑️ **Delete Memory** | Remove specific memories by ID | Memory cleanup |
| 📜 **Memory History** | View version history and changes | Memory tracking |

### 🎯 Use Cases

#### **Personal AI Assistant**
- Remember user preferences and past conversations
- Provide contextual responses based on memory
- Learn and adapt to user behavior over time

#### **Customer Support**
- Maintain customer interaction history
- Track issue resolution and follow-ups
- Provide personalized support experiences

#### **Knowledge Management**
- Store and organize important information
- Create searchable knowledge bases
- Maintain institutional memory

#### **Content Creation**
- Remember writing styles and preferences
- Track project progress and ideas
- Maintain creative context across sessions

### 🔗 API Integration

The plugin integrates with Mem0's powerful APIs:

- **V1 API**: Basic memory operations and search
- **V2 API**: Advanced features with filtering and pagination
- **Multimodal API**: Support for various content types
- **History API**: Version tracking and change management

### 🌐 Multi-language Support

- **English**: Complete interface and documentation
- **中文 (Chinese)**: Full localization for Chinese users
- **日本語 (Japanese)**: Japanese language support
- **Português (Portuguese)**: Portuguese localization

### 📖 Documentation

- **Installation Guide**: Step-by-step setup instructions
- **API Reference**: Complete tool documentation
- **Best Practices**: Optimization tips and recommendations
- **Troubleshooting**: Common issues and solutions

### 🤝 Support

- **GitHub Issues**: Report bugs and request features
- **Documentation**: Comprehensive guides and examples
- **Community**: Join discussions and share experiences

## � Secondary Development

This plugin represents a secondary development effort to enhance the original Mem0 plugin with:

### **Enhanced Features**
- **Visual Design**: Added emoji icons and improved UI/UX
- **Internationalization**: Complete 4-language support (EN/CN/JP/PT)
- **Documentation**: Comprehensive bilingual documentation
- **Tool Organization**: Logical categorization and improved workflow
- **Error Handling**: Enhanced error messages and user feedback
- **Performance**: Optimized API calls and response handling

### **Development Approach**
- **Respectful Enhancement**: Building upon the original architecture
- **Backward Compatibility**: Maintaining compatibility with existing workflows
- **Community Focus**: Open-source development with community contributions
- **Quality Assurance**: Comprehensive testing and validation

### **Contributing**
We welcome contributions to further enhance this plugin:
- **Bug Reports**: Submit issues via GitHub
- **Feature Requests**: Propose new functionality
- **Code Contributions**: Submit pull requests with improvements
- **Documentation**: Help improve guides and examples

### 📄 License

This plugin is open source and available under the MIT License.

---

**Version**: 0.2.0
**Author**: mocha (Secondary Development)
**Original Author**: blue
**Last Updated**: 2025-07-24


