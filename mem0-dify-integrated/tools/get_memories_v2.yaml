identity:
  name: get_mem0ai_memories_v2
  author: moch<PERSON> (Enhanced by mocha, Original by <PERSON><PERSON><PERSON>)
  label:
    en_US: 📋 Batch Memory Retrieval
    zh_Hans: 📋 批量记忆获取
    pt_BR: 📋 Recuperação em Lote
    ja_JP: 📋 バッチメモリ取得
description:
  human:
    en_US: Retrieve memories using V2 API with advanced filtering, pagination, and sorting capabilities.
    zh_Hans: 使用V2 API获取记忆，支持高级过滤、分页和排序功能。
    pt_BR: Recuperar memórias usando API V2 com recursos avançados de filtragem, paginação e classificação.
    ja_JP: 高度なフィルタリング、ページネーション、ソート機能を備えたV2 APIを使用してメモリを取得します。
  llm: This tool retrieves memories using the V2 API with advanced features like filtering, pagination, and sorting. It supports multiple identity types (user_id, agent_id, run_id) and returns detailed memory information with metadata.

parameters:
  - name: user_id
    type: string
    required: false
    label:
      en_US: User ID
      zh_Hans: 用户ID
      pt_BR: ID do Usuário
      ja_JP: ユーザーID
    human_description:
      en_US: Unique identifier for the user whose memories to retrieve
      zh_Hans: 要获取记忆的用户的唯一标识符
      pt_BR: Identificador único do usuário cujas memórias devem ser recuperadas
      ja_JP: メモリを取得するユーザーの一意識別子
    llm_description: The unique identifier for the user whose memories should be retrieved. Use this to get memories specific to a particular user.
    form: llm

  - name: agent_id
    type: string
    required: false
    label:
      en_US: Agent ID
      zh_Hans: 代理ID
      pt_BR: ID do Agente
      ja_JP: エージェントID
    human_description:
      en_US: Unique identifier for the AI agent whose memories to retrieve
      zh_Hans: 要获取记忆的AI代理的唯一标识符
      pt_BR: Identificador único do agente de IA cujas memórias devem ser recuperadas
      ja_JP: メモリを取得するAIエージェントの一意識別子
    llm_description: The unique identifier for the AI agent whose memories should be retrieved. Use this to get memories specific to a particular AI agent.
    form: llm

  - name: run_id
    type: string
    required: false
    label:
      en_US: Run ID
      zh_Hans: 运行ID
      pt_BR: ID da Execução
      ja_JP: 実行ID
    human_description:
      en_US: Unique identifier for the conversation session whose memories to retrieve
      zh_Hans: 要获取记忆的对话会话的唯一标识符
      pt_BR: Identificador único da sessão de conversa cujas memórias devem ser recuperadas
      ja_JP: メモリを取得する会話セッションの一意識別子
    llm_description: The unique identifier for the conversation session whose memories should be retrieved. Use this to get memories from a specific conversation session.
    form: llm

  - name: limit
    type: number
    required: false
    default: 50
    label:
      en_US: Limit
      zh_Hans: 限制数量
      pt_BR: Limite
      ja_JP: 制限
    human_description:
      en_US: Maximum number of memories to retrieve (1-100)
      zh_Hans: 要获取的记忆的最大数量（1-100）
      pt_BR: Número máximo de memórias a serem recuperadas (1-100)
      ja_JP: 取得するメモリの最大数（1-100）
    llm_description: The maximum number of memories to retrieve in a single request. Must be between 1 and 100.
    form: llm

  - name: offset
    type: number
    required: false
    default: 0
    label:
      en_US: Offset
      zh_Hans: 偏移量
      pt_BR: Deslocamento
      ja_JP: オフセット
    human_description:
      en_US: Number of memories to skip for pagination
      zh_Hans: 分页时要跳过的记忆数量
      pt_BR: Número de memórias a serem ignoradas para paginação
      ja_JP: ページネーションでスキップするメモリ数
    llm_description: The number of memories to skip for pagination. Use this with limit to implement pagination.
    form: llm

  - name: filters
    type: string
    required: false
    label:
      en_US: Filters
      zh_Hans: 过滤器
      pt_BR: Filtros
      ja_JP: フィルター
    human_description:
      en_US: 'JSON string for advanced filtering (e.g., {"category": "work", "score": {"$gte": 0.8}})'
      zh_Hans: '用于高级过滤的JSON字符串（例如：{"category": "work", "score": {"$gte": 0.8}}）'
      pt_BR: 'String JSON para filtragem avançada (ex: {"category": "work", "score": {"$gte": 0.8}})'
      ja_JP: '高度なフィルタリング用のJSON文字串（例：{"category": "work", "score": {"$gte": 0.8}}）'
    llm_description: A JSON string containing filter criteria for advanced memory filtering. Supports MongoDB-style query operators.
    form: llm

  - name: sort
    type: string
    required: false
    label:
      en_US: Sort
      zh_Hans: 排序
      pt_BR: Classificação
      ja_JP: ソート
    human_description:
      en_US: 'JSON string for sorting (e.g., {"created_at": -1, "score": 1})'
      zh_Hans: '用于排序的JSON字符串（例如：{"created_at": -1, "score": 1}）'
      pt_BR: 'String JSON para classificação (ex: {"created_at": -1, "score": 1})'
      ja_JP: 'ソート用のJSON文字列（例：{"created_at": -1, "score": 1}）'
    llm_description: A JSON string containing sort criteria. Use 1 for ascending and -1 for descending order.
    form: llm
extra:
  python:
    source: tools/get_memories_v2.py
