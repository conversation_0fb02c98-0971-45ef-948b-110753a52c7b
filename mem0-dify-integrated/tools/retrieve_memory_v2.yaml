identity:
  name: retrieve_mem0ai_memory_v2
  author: mocha (Enhanced by mocha, Original by <PERSON><PERSON><PERSON>)
  label:
    en_US: 🔍⚡ Advanced Memory Search
    zh_Hans: 🔍⚡ 高级记忆搜索
    pt_BR: 🔍⚡ Busca Avançada de Memória
    ja_JP: 🔍⚡ 高度メモリ検索
description:
  human:
    en_US: Advanced memory search with similarity thresholds, filtering, and pagination using V2 API
    zh_Hans: 使用V2 API进行高级记忆搜索，支持相似度阈值、过滤和分页
    pt_BR: Busca avançada de memória com limites de similaridade, filtragem e paginação usando API V2
    ja_JP: V2 APIを使用した類似度閾値、フィルタリング、ページネーション付きの高度メモリ検索
  llm: Advanced memory search with similarity thresholds, filtering, and pagination using V2 API for precise memory retrieval
parameters:
  - name: query
    type: string
    required: true
    label:
      en_US: Query string
      zh_Hans: 查询语句
      pt_BR: Query string
    human_description:
      en_US: The query string is used to search for specific memories within the system. It can be a keyword, phrase, or sentence that describes the memory you're looking for.
      zh_Hans: 查询语句用于在系统中搜索特定的记忆。它可以是一个关键词、短语或描述你正在寻找的记忆的句子。
      pt_BR: A string de consulta é usada para buscar memórias específicas dentro do sistema. Ela pode ser uma palavra-chave, frase ou sentença que descreve a memória que você está procurando.
    llm_description: The query string is used to search for specific memories within the system. It can be a keyword, phrase, or sentence that describes the memory you're looking for.
    form: llm
  - name: user_id
    type: string
    required: true
    label:
      en_US: User ID
      zh_Hans: 用户ID
      pt_BR: ID do Usuário
    human_description:
      en_US: User ID for memory filtering
      zh_Hans: 用于记忆过滤的用户ID
      pt_BR: ID do usuário para filtragem de memória
    llm_description: User ID for memory filtering
    form: llm
  - name: agent_id
    type: string
    required: false
    label:
      en_US: Agent ID
      zh_Hans: 代理ID
      pt_BR: ID do Agente
    human_description:
      en_US: Optional agent ID for filtering memories by specific agent
      zh_Hans: 可选的代理ID用于按特定代理过滤记忆
      pt_BR: ID do agente opcional para filtrar memórias por agente específico
    llm_description: Optional agent ID for filtering memories by specific agent
    form: llm
  - name: run_id
    type: string
    required: false
    label:
      en_US: Run ID
      zh_Hans: 运行ID
      pt_BR: ID da Execução
    human_description:
      en_US: Optional run ID for filtering memories by specific session
      zh_Hans: 可选的运行ID用于按特定会话过滤记忆
      pt_BR: ID de execução opcional para filtrar memórias por sessão específica
    llm_description: Optional run ID for filtering memories by specific session
    form: llm
  - name: limit
    type: number
    required: false
    default: 10
    label:
      en_US: Limit
      zh_Hans: 限制数量
      pt_BR: Limite
    human_description:
      en_US: Maximum number of memories to return (1-100)
      zh_Hans: 返回的最大记忆数量 (1-100)
      pt_BR: Número máximo de memórias a retornar (1-100)
    llm_description: Maximum number of memories to return (1-100)
    form: form
  - name: similarity_threshold
    type: number
    required: false
    default: 0.0
    label:
      en_US: Similarity Threshold
      zh_Hans: 相似度阈值
      pt_BR: Limite de Similaridade
    human_description:
      en_US: Minimum similarity score for results (0.0-1.0)
      zh_Hans: 结果的最小相似度分数 (0.0-1.0)
      pt_BR: Pontuação mínima de similaridade para resultados (0.0-1.0)
    llm_description: Minimum similarity score for results (0.0-1.0)
    form: form
  - name: filters
    type: string
    required: false
    label:
      en_US: Advanced Filters (JSON)
      zh_Hans: 高级过滤器 (JSON)
      pt_BR: Filtros Avançados (JSON)
    human_description:
      en_US: Advanced filters in JSON format for complex queries
      zh_Hans: JSON格式的高级过滤器用于复杂查询
      pt_BR: Filtros avançados em formato JSON para consultas complexas
    llm_description: Advanced filters in JSON format for complex queries (e.g., AND/OR logic with user_id and categories)
    form: llm
extra:
  python:
    source: tools/retrieve_memory_v2.py
