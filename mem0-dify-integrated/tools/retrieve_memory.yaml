identity:
  name: retrieve_mem0ai_memory
  author: moch<PERSON> (Enhanced by mocha, Original by ye<PERSON><PERSON>)
  label:
    en_US: 🔍 Retrieve Memory
    zh_Hans: 🔍 检索记忆
    pt_BR: 🔍 Recuperar Memória
    ja_JP: 🔍 メモリ検索
description:
  human:
    en_US: Search and retrieve stored memories using semantic similarity and keyword matching
    zh_Hans: 使用语义相似性和关键词匹配搜索和检索存储的记忆
    pt_BR: Pesquisar e recuperar memórias armazenadas usando similaridade semântica e correspondência de palavras-chave
    ja_JP: セマンティック類似性とキーワードマッチングを使用して保存されたメモリを検索・取得
  llm: Search and retrieve stored memories using semantic similarity and keyword matching to find relevant conversation context
parameters:
  - name: query
    type: string
    required: true
    label:
      en_US: Query string
      zh_Hans: 查询语句
      pt_BR: Query string
    human_description:
      en_US: The query string is used to search for specific memories within the system. It can be a keyword, phrase, or sentence that describes the memory you're looking for.
      zh_Hans: 查询语句用于在系统中搜索特定的记忆。它可以是一个关键词、短语或描述你正在寻找的记忆的句子。
      pt_BR: A string de consulta é usada para buscar memórias específicas dentro do sistema. Ela pode ser uma palavra-chave, frase ou sentença que descreve a memória que você está procurando.
    llm_description: The query string is used to search for specific memories within the system. It can be a keyword, phrase, or sentence that describes the memory you're looking for.
    form: llm
  - name: user_id
    type: string
    required: true
    label:
      en_US: User ID
      zh_Hans: 用户ID
      pt_BR: ID do Usuário
    human_description:
      en_US: User ID
      zh_Hans: 用户ID
      pt_BR: ID do Usuário
    llm_description: User ID
    form: llm
  - name: limit
    type: number
    required: false
    default: 10
    label:
      en_US: Limit
      zh_Hans: 限制数量
      pt_BR: Limite
    human_description:
      en_US: Maximum number of memories to return (1-100)
      zh_Hans: 返回的最大记忆数量 (1-100)
      pt_BR: Número máximo de memórias a retornar (1-100)
    llm_description: Maximum number of memories to return (1-100)
    form: form
  - name: filters
    type: string
    required: false
    label:
      en_US: Filters (JSON)
      zh_Hans: 过滤器 (JSON)
      pt_BR: Filtros (JSON)
    human_description:
      en_US: Optional filters in JSON format for advanced queries
      zh_Hans: 可选的JSON格式过滤器用于高级查询
      pt_BR: Filtros opcionais em formato JSON para consultas avançadas
    llm_description: Optional filters in JSON format for advanced queries
    form: llm
extra:
  python:
    source: tools/retrieve_memory.py
