identity:
  name: multimodal_add_mem0ai
  author: moch<PERSON> (Enhanced by mocha, Original by ye<PERSON><PERSON>)
  label:
    en_US: 🎨 Multimodal Memory
    zh_Hans: 🎨 多模态记忆
    pt_BR: 🎨 Memória Multimodal
    ja_JP: 🎨 マルチモーダルメモリ
description:
  human:
    en_US: Add multimodal memories (text, images, documents, PDFs) to Mem0. Supports all content types including text, image_url, mdx_url, and pdf_url. This is the unified tool for all multimodal content processing.
    zh_Hans: 向Mem0添加多模态记忆（文本、图像、文档、PDF）。支持所有内容类型，包括文本、image_url、mdx_url和pdf_url。这是处理所有多模态内容的统一工具。
    pt_BR: Adicionar memórias multimodais (texto, imagens, documentos, PDFs) ao Mem0. Suporta todos os tipos de conteúdo, incluindo texto, image_url, mdx_url e pdf_url. Esta é a ferramenta unificada para processamento de conteúdo multimodal.
    ja_JP: Mem0にマルチモーダルメモリ（テキスト、画像、文書、PDF）を追加します。テキスト、image_url、mdx_url、pdf_urlを含むすべてのコンテンツタイプをサポート。これはすべてのマルチモーダルコンテンツ処理のための統一ツールです。
  llm: This is the unified tool for adding all types of multimodal memories to Mem0. It supports text, images (image_url), documents (mdx_url), and PDFs (pdf_url). Use OpenAI chat message format with proper content type specifications for different media types.

parameters:
  - name: messages
    type: string
    required: true
    label:
      en_US: Messages
      zh_Hans: 消息
      pt_BR: Mensagens
      ja_JP: メッセージ
    human_description:
      en_US: 'JSON array of conversation messages. Examples: [{"role": "user", "content": "text"}] for text, [{"role": "user", "content": {"type": "image_url", "image_url": {"url": "https://example.com/image.jpg"}}}] for images, [{"role": "user", "content": {"type": "pdf_url", "pdf_url": {"url": "https://example.com/doc.pdf"}}}] for PDFs, [{"role": "user", "content": {"type": "mdx_url", "mdx_url": {"url": "https://example.com/doc.txt"}}}] for documents.'
      zh_Hans: 'JSON格式的对话消息数组。示例：文本使用[{"role": "user", "content": "text"}]，图像使用[{"role": "user", "content": {"type": "image_url", "image_url": {"url": "https://example.com/image.jpg"}}}]，PDF使用[{"role": "user", "content": {"type": "pdf_url", "pdf_url": {"url": "https://example.com/doc.pdf"}}}]，文档使用[{"role": "user", "content": {"type": "mdx_url", "mdx_url": {"url": "https://example.com/doc.txt"}}}]。'
      pt_BR: 'Array JSON de mensagens de conversa. Exemplos: [{"role": "user", "content": "text"}] para texto, [{"role": "user", "content": {"type": "image_url", "image_url": {"url": "https://example.com/image.jpg"}}}] para imagens, [{"role": "user", "content": {"type": "pdf_url", "pdf_url": {"url": "https://example.com/doc.pdf"}}}] para PDFs, [{"role": "user", "content": {"type": "mdx_url", "mdx_url": {"url": "https://example.com/doc.txt"}}}] para documentos.'
      ja_JP: 'JSON形式の会話メッセージ配列。例：テキストは[{"role": "user", "content": "text"}]、画像は[{"role": "user", "content": {"type": "image_url", "image_url": {"url": "https://example.com/image.jpg"}}}]、PDFは[{"role": "user", "content": {"type": "pdf_url", "pdf_url": {"url": "https://example.com/doc.pdf"}}}]、文書は[{"role": "user", "content": {"type": "mdx_url", "mdx_url": {"url": "https://example.com/doc.txt"}}}]。'
    llm_description: JSON array of conversation messages in OpenAI format. For text use string content, for multimodal use object with type field. Supported types - image_url for images, pdf_url for PDFs, mdx_url for documents/text files. Each message needs role and content fields.
    form: llm

  - name: user_id
    type: string
    required: false
    label:
      en_US: User ID
      zh_Hans: 用户ID
      pt_BR: ID do Usuário
      ja_JP: ユーザーID
    human_description:
      en_US: Unique identifier for the user
      zh_Hans: 用户的唯一标识符
      pt_BR: Identificador único do usuário
      ja_JP: ユーザーの一意識別子
    llm_description: The unique identifier for the user whose memories are being added.
    form: llm

  - name: agent_id
    type: string
    required: false
    label:
      en_US: Agent ID
      zh_Hans: 代理ID
      pt_BR: ID do Agente
      ja_JP: エージェントID
    human_description:
      en_US: Unique identifier for the AI agent
      zh_Hans: AI代理的唯一标识符
      pt_BR: Identificador único do agente de IA
      ja_JP: AIエージェントの一意識別子
    llm_description: The unique identifier for the AI agent whose memories are being added.
    form: llm

  - name: run_id
    type: string
    required: false
    label:
      en_US: Run ID
      zh_Hans: 运行ID
      pt_BR: ID da Execução
      ja_JP: 実行ID
    human_description:
      en_US: Unique identifier for the conversation session
      zh_Hans: 对话会话的唯一标识符
      pt_BR: Identificador único da sessão de conversa
      ja_JP: 会話セッションの一意識別子
    llm_description: The unique identifier for the conversation session.
    form: llm

  - name: metadata
    type: string
    required: false
    label:
      en_US: Metadata
      zh_Hans: 元数据
      pt_BR: Metadados
      ja_JP: メタデータ
    human_description:
      en_US: 'Additional metadata as JSON string (e.g., {"source": "chat", "priority": "high"})'
      zh_Hans: '作为JSON字符串的附加元数据（例如：{"source": "chat", "priority": "high"}）'
      pt_BR: 'Metadados adicionais como string JSON (ex: {"source": "chat", "priority": "high"})'
      ja_JP: 'JSON文字列としての追加メタデータ（例：{"source": "chat", "priority": "high"}）'
    llm_description: Additional metadata to associate with the memories as a JSON string.
    form: llm
extra:
  python:
    source: tools/multimodal_add.py
