from collections.abc import Generator
from typing import Any
import httpx
import time
import json
from dify_plugin import Tool
from dify_plugin.entities.tool import ToolInvokeMessage

class MultimodalAddMem0Tool(Tool):
    def _invoke(self, tool_parameters: dict[str, Any]) -> Generator[ToolInvokeMessage, None, None]:
        # Get API key from credentials
        api_key = self.runtime.credentials["mem0_api_key"]
        
        # Get API URL from credentials or use default (consistent with provider/mem0.yaml)

        api_url = self.runtime.credentials.get("mem0_api_url", "http://localhost:8000")

        if not api_url or api_url.strip() == "":

            api_url = "http://localhost:8000"

        api_url = api_url.rstrip("/")
        
        # Extract parameters
        messages = tool_parameters["messages"]
        user_id = tool_parameters.get("user_id")
        agent_id = tool_parameters.get("agent_id")
        run_id = tool_parameters.get("run_id")
        metadata = tool_parameters.get("metadata")
        
        # Validate messages
        if not messages or not messages.strip():
            yield self.create_json_message({
                "status": "error",
                "error": "Messages cannot be empty"
            })
            yield self.create_text_message("Error: Messages cannot be empty")
            return
        
        # Parse messages if it's a JSON string
        try:
            if isinstance(messages, str):
                parsed_messages = json.loads(messages)
            else:
                parsed_messages = messages
        except json.JSONDecodeError:
            # If not JSON, treat as simple text message
            parsed_messages = [{"role": "user", "content": messages}]
        
        # Prepare payload
        payload = {
            "messages": parsed_messages
        }
        
        # Add identity parameters
        if user_id:
            payload["user_id"] = user_id
        if agent_id:
            payload["agent_id"] = agent_id
        if run_id:
            payload["run_id"] = run_id
        
        # Add metadata if provided
        if metadata:
            try:
                if isinstance(metadata, str):
                    payload["metadata"] = json.loads(metadata)
                else:
                    payload["metadata"] = metadata
            except json.JSONDecodeError:
                yield self.create_json_message({
                    "status": "error",
                    "error": "Invalid metadata format. Must be valid JSON."
                })
                yield self.create_text_message("Error: Invalid metadata format. Must be valid JSON.")
                return

        # 记录添加操作
        identity = user_id or agent_id or run_id or "unknown"
        message_preview = str(parsed_messages)[:100] + "..." if len(str(parsed_messages)) > 100 else str(parsed_messages)        
        # 计时开始
        start_time = time.time()
        
        # Make HTTP request to add multimodal memory
        try:
            response = httpx.post(
                f"{api_url}/v1/memories/",
                json=payload,
                headers={"Authorization": f"Token {api_key}"},
                timeout=30  # 多模态处理需要更长时间
            )
            
            # 计算请求耗时
            request_time = time.time() - start_time
            response.raise_for_status()
            
            # Parse response
            response_data = response.json()
            
            # Extract memory information
            if isinstance(response_data, dict):
                if "results" in response_data:
                    # V1 API format
                    memories = response_data["results"]
                elif "memories" in response_data:
                    # V2 API format
                    memories = response_data["memories"]
                else:
                    # Single memory format
                    memories = [response_data]
            else:
                memories = response_data if isinstance(response_data, list) else [response_data]
            
            # Process memories
            processed_memories = []
            for memory in memories:
                if isinstance(memory, dict):
                    item = {
                        "id": memory.get("id", "unknown"),
                        "memory": memory.get("memory", ""),
                        "score": memory.get("score", 0.0),
                        "categories": memory.get("categories", []),
                        "created_at": memory.get("created_at", ""),
                        "metadata": memory.get("metadata", {}),
                        "user_id": memory.get("user_id"),
                        "agent_id": memory.get("agent_id"),
                        "run_id": memory.get("run_id")
                    }
                    processed_memories.append(item)
            
            # 记录添加结果
            # 计算总耗时
            total_time = time.time() - start_time
            yield self.create_json_message({
                "status": "success",
                "identity": identity,
                "memories_created": len(processed_memories),
                "memories": processed_memories,
                "original_messages": parsed_messages,
                "metadata": payload.get("metadata")
            })
            
            # Return text format
            text_response = f"Multimodal memory added successfully.\n\n"
            text_response += f"Identity: {identity}\n"
            text_response += f"Memories Created: {len(processed_memories)}\n"
            text_response += f"Processing Time: {total_time:.3f}s\n\n"
            
            if processed_memories:
                text_response += "Created Memories:\n"
                for idx, memory in enumerate(processed_memories, 1):
                    text_response += f"\n{idx}. ID: {memory['id']}"
                    text_response += f"\n   Memory: {memory['memory'][:100]}{'...' if len(memory['memory']) > 100 else ''}"
                    text_response += f"\n   Score: {memory['score']:.2f}"
                    text_response += f"\n   Categories: {', '.join(memory.get('categories', []))}"
                
            yield self.create_text_message(text_response)
            
        except httpx.HTTPStatusError as e:
            error_message = f"HTTP error: {e.response.status_code}"
            try:
                error_data = e.response.json()
                if "detail" in error_data:
                    error_message = f"Error: {error_data['detail']}"
            except:
                pass            
            yield self.create_json_message({
                "status": "error",
                "error": error_message,
                "identity": identity
            })
            
            yield self.create_text_message(f"Failed to add multimodal memory: {error_message}")
            
        except Exception as e:
            error_message = f"Error: {str(e)}"            
            yield self.create_json_message({
                "status": "error",
                "error": error_message,
                "identity": identity
            })
            
            yield self.create_text_message(f"Failed to add multimodal memory: {error_message}")
