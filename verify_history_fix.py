#!/usr/bin/env python3
"""
历史数据库修复验证脚本

用于验证历史数据库修复后的效果，检查空数据问题是否得到解决。
"""

import sqlite3
import sys
import os
from datetime import datetime

def test_database_improvements():
    """测试数据库改进效果"""
    
    db_path = "/opt/mem0ai/server/data/mem0/history.db"
    
    if not os.path.exists(db_path):
        print("❌ 历史数据库不存在")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 开始验证历史数据库修复效果...")
        print("=" * 60)
        
        # 1. 检查表结构
        print("1. 验证表结构...")
        cursor.execute("PRAGMA table_info(history)")
        columns = {row[1]: row[2] for row in cursor.fetchall()}
        
        expected_columns = {
            'id', 'memory_id', 'old_memory', 'new_memory', 'event',
            'created_at', 'updated_at', 'is_deleted', 'actor_id', 
            'role', 'response_time', 'metadata'
        }
        
        if set(columns.keys()) >= expected_columns:
            print("   ✅ 表结构包含所有必需的字段")
        else:
            missing = expected_columns - set(columns.keys())
            print(f"   ❌ 表结构缺少字段: {missing}")
            return False
        
        # 2. 检查数据完整性
        print("\n2. 验证数据完整性...")
        
        # 统计总记录数
        cursor.execute("SELECT COUNT(*) FROM history")
        total_records = cursor.fetchone()[0]
        print(f"   📊 总记录数: {total_records}")
        
        if total_records == 0:
            print("   ⚠️  数据库为空，无法进行完整性验证")
            return True
        
        # 检查各字段的空值情况
        null_checks = {
            'memory_id': "memory_id IS NULL OR memory_id = ''",
            'event': "event IS NULL OR event = ''", 
            'role': "role IS NULL OR role = ''",
            'metadata': "metadata IS NULL OR metadata = ''",
            'actor_id': "actor_id IS NULL OR actor_id = ''"
        }
        
        results = {}
        for field, condition in null_checks.items():
            cursor.execute(f"SELECT COUNT(*) FROM history WHERE {condition}")
            null_count = cursor.fetchone()[0]
            null_percentage = (null_count / total_records) * 100
            results[field] = {'count': null_count, 'percentage': null_percentage}
            
        print("\n   📈 字段空值统计:")
        for field, stats in results.items():
            status = "✅" if stats['percentage'] < 10 else "⚠️" if stats['percentage'] < 50 else "❌"
            print(f"   {status} {field}: {stats['count']}/{total_records} ({stats['percentage']:.1f}%)")
        
        # 3. 检查事件类型分布
        print("\n3. 验证事件类型分布...")
        cursor.execute("SELECT event, COUNT(*) FROM history GROUP BY event ORDER BY COUNT(*) DESC")
        events = cursor.fetchall()
        
        for event, count in events:
            percentage = (count / total_records) * 100
            print(f"   📌 {event}: {count} ({percentage:.1f}%)")
        
        # 4. 检查最近的记录是否包含完整metadata
        print("\n4. 验证最新记录的metadata完整性...")
        cursor.execute("""
            SELECT id, event, role, metadata, created_at 
            FROM history 
            WHERE created_at IS NOT NULL 
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        recent_records = cursor.fetchall()
        
        if recent_records:
            print("   📋 最近5条记录的metadata状态:")
            for record in recent_records:
                record_id, event, role, metadata, created_at = record
                metadata_status = "✅ 有" if metadata else "❌ 无" 
                role_status = "✅ 有" if role else "❌ 无"
                print(f"   - {record_id[:8]}... ({event}): role={role_status}, metadata={metadata_status}")
        
        # 5. 数据库健康度评分
        print("\n5. 计算数据库健康度评分...")
        
        # 评分标准
        health_score = 100
        
        # 扣分项
        if results['role']['percentage'] > 90:
            health_score -= 30
        elif results['role']['percentage'] > 50:
            health_score -= 15
        elif results['role']['percentage'] > 10:
            health_score -= 5
            
        if results['metadata']['percentage'] > 90:
            health_score -= 25
        elif results['metadata']['percentage'] > 50:
            health_score -= 12
        elif results['metadata']['percentage'] > 10:
            health_score -= 3
            
        if results['actor_id']['percentage'] > 90:
            health_score -= 20
        elif results['actor_id']['percentage'] > 50:
            health_score -= 10
        elif results['actor_id']['percentage'] > 10:
            health_score -= 2
        
        print(f"\n   🎯 数据库健康度评分: {health_score}/100")
        
        if health_score >= 90:
            print("   🟢 数据库状态: 优秀")
        elif health_score >= 70:
            print("   🟡 数据库状态: 良好") 
        elif health_score >= 50:
            print("   🟠 数据库状态: 一般")
        else:
            print("   🔴 数据库状态: 需要改进")
        
        conn.close()
        
        print("\n" + "=" * 60)
        print("✅ 历史数据库验证完成")
        
        return health_score >= 70
        
    except Exception as e:
        print(f"❌ 验证过程中出现错误: {e}")
        return False


def test_new_functionality():
    """测试新功能是否正常工作"""
    
    print("\n🧪 测试新的HistoryManager功能...")
    
    try:
        # 导入测试
        sys.path.append('/opt/mem0ai')
        from mem0.memory.history_manager import HistoryManager, HistoryManagerFactory
        from mem0.memory.storage import SQLiteManager
        
        print("   ✅ HistoryManager 导入成功")
        
        # 创建测试实例
        db_path = "/opt/mem0ai/server/data/mem0/history.db"
        storage = SQLiteManager(db_path)
        history_manager = HistoryManagerFactory.create_from_existing_storage(storage)
        
        print("   ✅ HistoryManager 实例创建成功")
        
        # 测试方法
        test_methods = [
            'record_add_memory',
            'record_update_memory', 
            'record_delete_memory',
            'record_search_operation',
            'get_memory_history',
            'get_history_statistics'
        ]
        
        for method in test_methods:
            if hasattr(history_manager, method):
                print(f"   ✅ 方法 {method} 存在")
            else:
                print(f"   ❌ 方法 {method} 不存在")
                return False
        
        storage.close()        
        print("   ✅ 所有新功能测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 新功能测试失败: {e}")
        return False


def main():
    """主函数"""
    
    print("🚀 Mem0 历史数据库修复验证")
    print(f"⏰ 验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("")
    
    # 验证数据库改进
    db_ok = test_database_improvements()
    
    # 测试新功能
    func_ok = test_new_functionality()
    
    print(f"\n📋 验证结果汇总:")
    print(f"   - 数据库改进: {'✅ 通过' if db_ok else '❌ 失败'}")
    print(f"   - 新功能测试: {'✅ 通过' if func_ok else '❌ 失败'}")
    
    if db_ok and func_ok:
        print(f"\n🎉 恭喜！历史数据库修复验证全部通过")
        return 0
    else:
        print(f"\n⚠️  部分验证失败，需要进一步检查")
        return 1


if __name__ == "__main__":
    sys.exit(main())