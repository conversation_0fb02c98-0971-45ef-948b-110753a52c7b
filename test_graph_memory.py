#!/usr/bin/env python3
"""
图谱记忆功能测试脚本
测试Mem0的图谱记忆功能，包括添加、搜索和检索带有关系的记忆
"""

import requests
import json
import time
from typing import Dict, List, Any

class GraphMemoryTester:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.user_id = "joseph_test"
        
    def test_health(self) -> bool:
        """测试API健康状态"""
        try:
            response = requests.get(f"{self.base_url}/health")
            print(f"✅ API健康状态: {response.status_code}")
            return response.status_code == 200
        except Exception as e:
            print(f"❌ API连接失败: {e}")
            return False
    
    def add_memory_with_graph(self, messages: List[Dict], enable_graph: bool = True) -> Dict:
        """添加带图谱功能的记忆"""
        url = f"{self.base_url}/v1/memories/"
        payload = {
            "messages": messages,
            "user_id": self.user_id,
            "version": "v1",
            "enable_graph": enable_graph,
            "output_format": "v1.1"
        }
        
        print(f"\n📝 添加记忆 (enable_graph={enable_graph}):")
        print(f"   消息: {messages[-1]['content'] if messages else 'N/A'}")
        
        try:
            response = requests.post(url, json=payload)
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 成功添加 {len(result.get('results', []))} 条记忆")
                return result
            else:
                print(f"❌ 添加失败: {response.status_code} - {response.text}")
                return {}
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return {}
    
    def search_memory_with_graph(self, query: str, enable_graph: bool = True) -> Dict:
        """使用图谱功能搜索记忆"""
        url = f"{self.base_url}/v1/memories/search/"
        payload = {
            "query": query,
            "user_id": self.user_id,
            "enable_graph": enable_graph,
            "output_format": "v1.1"
        }
        
        print(f"\n🔍 搜索记忆 (enable_graph={enable_graph}):")
        print(f"   查询: {query}")
        
        try:
            response = requests.post(url, json=payload)
            if response.status_code == 200:
                result = response.json()
                # 处理不同的响应格式
                if isinstance(result, list):
                    result = {"results": result, "relations": []}
                print(f"✅ 找到 {len(result.get('results', []))} 条相关记忆")
                if result.get('relations'):
                    print(f"   图谱关系: {len(result['relations'])} 个")
                return result
            else:
                print(f"❌ 搜索失败: {response.status_code} - {response.text}")
                return {}
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return {}
    
    def get_all_memories_with_graph(self, enable_graph: bool = True) -> Dict:
        """获取所有记忆及图谱关系"""
        url = f"{self.base_url}/v1/memories/"
        params = {
            "user_id": self.user_id,
            "enable_graph": enable_graph,
            "output_format": "v1.1"
        }
        
        print(f"\n📋 获取所有记忆 (enable_graph={enable_graph}):")
        
        try:
            response = requests.get(url, params=params)
            if response.status_code == 200:
                result = response.json()
                # 处理不同的响应格式
                if isinstance(result, list):
                    result = {"results": result, "relations": []}
                print(f"✅ 获取到 {len(result.get('results', []))} 条记忆")
                if result.get('relations'):
                    print(f"   图谱关系: {len(result['relations'])} 个")
                return result
            else:
                print(f"❌ 获取失败: {response.status_code} - {response.text}")
                return {}
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return {}
    
    def print_relations(self, relations: List[Dict]):
        """打印图谱关系"""
        if not relations:
            print("   无图谱关系")
            return
            
        print("   图谱关系:")
        for rel in relations:
            source = rel.get('source', 'N/A')
            relationship = rel.get('relationship', 'N/A')
            target = rel.get('target', 'N/A')
            source_type = rel.get('source_type', 'N/A')
            target_type = rel.get('target_type', 'N/A')
            score = rel.get('score', 'N/A')
            
            print(f"     {source}({source_type}) --[{relationship}]--> {target}({target_type})")
            if score != 'N/A':
                print(f"       相关度分数: {score}")
    
    def clear_user_memories(self):
        """清理测试用户的记忆"""
        print(f"\n🧹 清理用户 {self.user_id} 的记忆...")
        
        # 先获取所有记忆
        memories = self.get_all_memories_with_graph(enable_graph=False)
        if not memories.get('results'):
            print("   没有需要清理的记忆")
            return
        
        # 删除每个记忆
        deleted_count = 0
        for memory in memories['results']:
            memory_id = memory.get('id')
            if memory_id:
                try:
                    url = f"{self.base_url}/v1/memories/{memory_id}/"
                    response = requests.delete(url)
                    if response.status_code == 200:
                        deleted_count += 1
                except Exception as e:
                    print(f"   删除记忆 {memory_id} 时出错: {e}")
        
        print(f"✅ 成功删除 {deleted_count} 条记忆")
    
    def run_comprehensive_test(self):
        """运行完整的图谱记忆测试"""
        print("=" * 60)
        print("🧪 开始图谱记忆功能测试")
        print("=" * 60)
        
        # 1. 健康检查
        if not self.test_health():
            print("❌ API服务不可用，测试终止")
            return
        
        # 2. 清理现有记忆
        self.clear_user_memories()
        
        # 3. 添加基础记忆
        print("\n" + "=" * 40)
        print("📝 第一阶段：添加基础个人信息")
        print("=" * 40)
        
        basic_messages = [
            {"role": "user", "content": "My name is Joseph"},
            {"role": "assistant", "content": "Hello Joseph, it's nice to meet you!"}
        ]
        self.add_memory_with_graph(basic_messages)
        
        # 等待处理
        time.sleep(2)
        
        # 4. 添加更多相关信息
        location_messages = [
            {"role": "user", "content": "I'm from Seattle and I work as a software engineer"},
            {"role": "assistant", "content": "That's great! Seattle is a wonderful city for tech professionals."}
        ]
        self.add_memory_with_graph(location_messages)
        
        time.sleep(2)
        
        # 5. 添加项目信息
        project_messages = [
            {"role": "user", "content": "I'm currently working on a machine learning project using Python and TensorFlow"},
            {"role": "assistant", "content": "Machine learning with Python and TensorFlow is a powerful combination!"}
        ]
        self.add_memory_with_graph(project_messages)
        
        time.sleep(2)
        
        # 6. 测试搜索功能
        print("\n" + "=" * 40)
        print("🔍 第二阶段：测试图谱搜索功能")
        print("=" * 40)
        
        # 搜索姓名
        result1 = self.search_memory_with_graph("what is my name?")
        if result1.get('relations'):
            self.print_relations(result1['relations'])
        
        # 搜索工作相关
        result2 = self.search_memory_with_graph("what do I do for work?")
        if result2.get('relations'):
            self.print_relations(result2['relations'])
        
        # 搜索技术栈
        result3 = self.search_memory_with_graph("what technologies am I using?")
        if result3.get('relations'):
            self.print_relations(result3['relations'])
        
        # 7. 获取所有记忆及关系
        print("\n" + "=" * 40)
        print("📋 第三阶段：获取完整图谱")
        print("=" * 40)
        
        all_memories = self.get_all_memories_with_graph()
        if all_memories.get('relations'):
            self.print_relations(all_memories['relations'])
        
        # 8. 对比测试（关闭图谱功能）
        print("\n" + "=" * 40)
        print("🔄 第四阶段：对比测试（无图谱）")
        print("=" * 40)
        
        result_no_graph = self.search_memory_with_graph("what is my name?", enable_graph=False)
        print("   无图谱模式的搜索结果（应该没有relations字段）")
        
        print("\n" + "=" * 60)
        print("✅ 图谱记忆功能测试完成")
        print("=" * 60)
        
        # 总结
        print("\n📊 测试总结:")
        total_memories = len(all_memories.get('results', []))
        total_relations = len(all_memories.get('relations', []))
        print(f"   • 总记忆数: {total_memories}")
        print(f"   • 图谱关系数: {total_relations}")
        print(f"   • 图谱功能: {'✅ 正常' if total_relations > 0 else '❌ 异常'}")

if __name__ == "__main__":
    tester = GraphMemoryTester()
    tester.run_comprehensive_test()