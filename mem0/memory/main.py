import asyncio
import concurrent
import gc
import hashlib
import json
import logging
import os
import time
import uuid
import warnings
from copy import deepcopy
from datetime import datetime
from typing import Any, Dict, List, Optional

import pytz
from pydantic import ValidationError

from mem0.configs.base import MemoryConfig, MemoryItem
from mem0.configs.enums import MemoryType
from mem0.configs.prompts import (
    PROCEDURAL_MEMORY_SYSTEM_PROMPT,
    get_update_memory_messages,
)
from mem0.memory.base import MemoryBase
from mem0.memory.setup import mem0_dir, setup_config
from mem0.memory.storage import SQLiteManager
from mem0.memory.history_manager import HistoryManager, HistoryManagerFactory
from mem0.memory.telemetry import capture_event
from mem0.memory.utils import (
    get_fact_retrieval_messages,
    parse_messages,
    parse_vision_messages,
    process_telemetry_filters,
    remove_code_blocks,
)
from mem0.utils.factory import EmbedderFactory, LlmFactory, VectorStoreFactory

# Import advanced retrieval for enhanced search capabilities
try:
    from mem0.retrieval import AdvancedRetrieval
except ImportError:
    AdvancedRetrieval = None
    logging.warning("AdvancedRetrieval not available. Advanced search features will be disabled.")

# Import performance monitoring
try:
    from mem0.retrieval.performance import PerformanceMonitor
except ImportError:
    PerformanceMonitor = None
    logging.warning("PerformanceMonitor not available. Performance monitoring will be disabled.")


def _build_filters_and_metadata(
    *,  # Enforce keyword-only arguments
    user_id: Optional[str] = None,
    agent_id: Optional[str] = None,
    run_id: Optional[str] = None,
    actor_id: Optional[str] = None,  # For query-time filtering
    input_metadata: Optional[Dict[str, Any]] = None,
    input_filters: Optional[Dict[str, Any]] = None,
) -> tuple[Dict[str, Any], Dict[str, Any]]:
    """
    Constructs metadata for storage and filters for querying based on session and actor identifiers.

    This helper supports multiple session identifiers (`user_id`, `agent_id`, and/or `run_id`)
    for flexible session scoping and optionally narrows queries to a specific `actor_id`. It returns two dicts:

    1. `base_metadata_template`: Used as a template for metadata when storing new memories.
       It includes all provided session identifier(s) and any `input_metadata`.
    2. `effective_query_filters`: Used for querying existing memories. It includes all
       provided session identifier(s), any `input_filters`, and a resolved actor
       identifier for targeted filtering if specified by any actor-related inputs.

    Actor filtering precedence: explicit `actor_id` arg → `filters["actor_id"]`
    This resolved actor ID is used for querying but is not added to `base_metadata_template`,
    as the actor for storage is typically derived from message content at a later stage.

    Args:
        user_id (Optional[str]): User identifier, for session scoping.
        agent_id (Optional[str]): Agent identifier, for session scoping.
        run_id (Optional[str]): Run identifier, for session scoping.
        actor_id (Optional[str]): Explicit actor identifier, used as a potential source for
            actor-specific filtering. See actor resolution precedence in the main description.
        input_metadata (Optional[Dict[str, Any]]): Base dictionary to be augmented with
            session identifiers for the storage metadata template. Defaults to an empty dict.
        input_filters (Optional[Dict[str, Any]]): Base dictionary to be augmented with
            session and actor identifiers for query filters. Defaults to an empty dict.

    Returns:
        tuple[Dict[str, Any], Dict[str, Any]]: A tuple containing:
            - base_metadata_template (Dict[str, Any]): Metadata template for storing memories,
              scoped to the provided session(s).
            - effective_query_filters (Dict[str, Any]): Filters for querying memories,
              scoped to the provided session(s) and potentially a resolved actor.
    """

    base_metadata_template = deepcopy(input_metadata) if input_metadata else {}
    effective_query_filters = deepcopy(input_filters) if input_filters else {}

    # ---------- add all provided session ids ----------
    session_ids_provided = []

    if user_id:
        base_metadata_template["user_id"] = user_id
        effective_query_filters["user_id"] = user_id
        session_ids_provided.append("user_id")

    if agent_id:
        base_metadata_template["agent_id"] = agent_id
        effective_query_filters["agent_id"] = agent_id
        session_ids_provided.append("agent_id")

    if run_id:
        base_metadata_template["run_id"] = run_id
        effective_query_filters["run_id"] = run_id
        session_ids_provided.append("run_id")

    if not session_ids_provided:
        raise ValueError("At least one of 'user_id', 'agent_id', or 'run_id' must be provided.")

    # ---------- optional actor filter ----------
    resolved_actor_id = actor_id or effective_query_filters.get("actor_id")
    if resolved_actor_id:
        effective_query_filters["actor_id"] = resolved_actor_id

    return base_metadata_template, effective_query_filters


setup_config()
logger = logging.getLogger(__name__)


class Memory(MemoryBase):
    def __init__(self, config: MemoryConfig = MemoryConfig()):
        self.config = config

        self.custom_fact_extraction_prompt = self.config.custom_fact_extraction_prompt
        self.custom_update_memory_prompt = self.config.custom_update_memory_prompt
        self.embedding_model = EmbedderFactory.create(
            self.config.embedder.provider,
            self.config.embedder.config,
            self.config.vector_store.config,
        )
        self.vector_store = VectorStoreFactory.create(
            self.config.vector_store.provider, self.config.vector_store.config
        )
        self.llm = LlmFactory.create(self.config.llm.provider, self.config.llm.config)
        self.db = SQLiteManager(self.config.history_db_path)
        self.history_manager = HistoryManagerFactory.create_from_existing_storage(self.db)
        self.collection_name = self.config.vector_store.config.collection_name
        self.api_version = self.config.version

        self.enable_graph = False

        if self.config.graph_store.config:
            if self.config.graph_store.provider == "memgraph":
                from mem0.memory.memgraph_memory import MemoryGraph
            elif self.config.graph_store.provider == "neptune":
                from mem0.graphs.neptune.main import MemoryGraph
            else:
                from mem0.memory.graph_memory import MemoryGraph

            self.graph = MemoryGraph(self.config)
            self.enable_graph = True
        else:
            self.graph = None
        self.config.vector_store.config.collection_name = "mem0migrations"
        if self.config.vector_store.provider in ["faiss", "qdrant"]:
            provider_path = f"migrations_{self.config.vector_store.provider}"
            self.config.vector_store.config.path = os.path.join(mem0_dir, provider_path)
            os.makedirs(self.config.vector_store.config.path, exist_ok=True)
        self._telemetry_vector_store = VectorStoreFactory.create(
            self.config.vector_store.provider, self.config.vector_store.config
        )

        # Initialize performance optimization features
        self._contextual_history_cache = {}  # Cache for historical messages
        self._cache_max_size = 100  # Maximum cache entries
        self._cache_ttl = 300  # Cache TTL in seconds (5 minutes)

        capture_event("mem0.init", self, {"sync_type": "sync"})

    @classmethod
    def from_config(cls, config_dict: Dict[str, Any]):
        try:
            config = cls._process_config(config_dict)
            config = MemoryConfig(**config_dict)
        except ValidationError as e:
            logger.error(f"Configuration validation error: {e}")
            raise
        return cls(config)

    @staticmethod
    def _process_config(config_dict: Dict[str, Any]) -> Dict[str, Any]:
        if "graph_store" in config_dict:
            if "vector_store" not in config_dict and "embedder" in config_dict:
                config_dict["vector_store"] = {}
                config_dict["vector_store"]["config"] = {}
                config_dict["vector_store"]["config"]["embedding_model_dims"] = config_dict["embedder"]["config"][
                    "embedding_dims"
                ]
        try:
            return config_dict
        except ValidationError as e:
            logger.error(f"Configuration validation error: {e}")
            raise

    def add(
        self,
        messages,
        *,
        user_id: Optional[str] = None,
        agent_id: Optional[str] = None,
        run_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        custom_categories: Optional[List[Dict[str, str]]] = None,
        infer: bool = True,
        memory_type: Optional[str] = None,
        prompt: Optional[str] = None,
        version: Optional[str] = "v1",
        includes: Optional[str] = None,
        excludes: Optional[str] = None,
        timestamp: Optional[int] = None,
    ):
        """
        Create a new memory.

        Adds new memories scoped to a single session id (e.g. `user_id`, `agent_id`, or `run_id`). One of those ids is required.

        Args:
            messages (str or List[Dict[str, str]]): The message content or list of messages
                (e.g., `[{"role": "user", "content": "Hello"}, {"role": "assistant", "content": "Hi"}]`)
                to be processed and stored.
            user_id (str, optional): ID of the user creating the memory. Defaults to None.
            agent_id (str, optional): ID of the agent creating the memory. Defaults to None.
            run_id (str, optional): ID of the run creating the memory. Defaults to None.
            metadata (dict, optional): Metadata to store with the memory. Defaults to None.
            infer (bool, optional): If True (default), an LLM is used to extract key facts from
                'messages' and decide whether to add, update, or delete related memories.
                If False, 'messages' are added as raw memories directly.
            memory_type (str, optional): Specifies the type of memory. Currently, only
                `MemoryType.PROCEDURAL.value` ("procedural_memory") is explicitly handled for
                creating procedural memories (typically requires 'agent_id'). Otherwise, memories
                are treated as general conversational/factual memories.memory_type (str, optional): Type of memory to create. Defaults to None. By default, it creates the short term memories and long term (semantic and episodic) memories. Pass "procedural_memory" to create procedural memories.
            prompt (str, optional): Prompt to use for the memory creation. Defaults to None.
            version (str, optional): API version for memory creation. "v1" (default) for standard
                behavior, "v2" for contextual add with automatic history retrieval. Defaults to "v1".
            includes (str, optional): Include only specific types of memories. When provided, only
                information related to this topic will be extracted and stored. Defaults to None.
            excludes (str, optional): Exclude specific types of memories. When provided, information
                related to this topic will be ignored during extraction. Defaults to None.

        Returns:
            dict: A dictionary containing the result of the memory addition operation, typically
                  including a list of memory items affected (added, updated) under a "results" key,
                  and potentially "relations" if graph store is enabled.
                  Example for v1.1+: `{"results": [{"id": "...", "memory": "...", "event": "ADD"}]}`
        """

        processed_metadata, effective_filters = _build_filters_and_metadata(
            user_id=user_id,
            agent_id=agent_id,
            run_id=run_id,
            input_metadata=metadata,
        )

        # Validate version parameter
        if version not in ["v1", "v2"]:
            raise ValueError(f"Invalid version '{version}'. Supported versions: v1, v2")

        # Add version information to metadata for storage
        processed_metadata["api_version"] = version
        if version == "v2":
            # Store original messages for v2 contextual add
            processed_metadata["original_messages"] = messages

        # Version-specific processing
        if version == "v2":
            # Start performance monitoring for v2 processing
            v2_start_time = time.time() if PerformanceMonitor else None

            try:
                # Implement v2 contextual add logic
                import logging
                logging.info("Processing v2 contextual add with automatic history retrieval")

                # Retrieve historical context
                historical_messages = self._retrieve_contextual_history(effective_filters, limit=10)

                # Merge historical context with new messages
                contextualized_messages = self._merge_historical_context(historical_messages, messages)

                # Use the merged messages for processing instead of original messages
                messages = contextualized_messages

                # Add telemetry event for v2 processing
                capture_event("mem0.contextual_add_v2", self, {
                    "historical_count": len(historical_messages),
                    "new_count": len(processed_metadata.get("original_messages", [])),
                    "merged_count": len(contextualized_messages),
                    "sync_type": "sync"
                })

                logging.info(f"v2 contextual add: merged {len(historical_messages)} historical + {len(processed_metadata.get('original_messages', []))} new messages into {len(contextualized_messages)} total messages")

                # Log v2 processing performance
                if PerformanceMonitor and v2_start_time:
                    v2_elapsed_ms = (time.time() - v2_start_time) * 1000
                    if v2_elapsed_ms > 800:  # Target: <800ms
                        logging.warning(f"v2 contextual add exceeded target: {v2_elapsed_ms:.2f}ms > 800ms")
                    else:
                        logging.info(f"v2 contextual add completed in {v2_elapsed_ms:.2f}ms")

            except Exception as e:
                # Graceful degradation: fall back to v1 behavior on error
                logging.warning(f"v2 contextual add failed, falling back to v1 behavior: {e}")
                capture_event("mem0.contextual_add_v2_fallback", self, {
                    "error": str(e),
                    "sync_type": "sync"
                })

        if memory_type is not None and memory_type != MemoryType.PROCEDURAL.value:
            raise ValueError(
                f"Invalid 'memory_type'. Please pass {MemoryType.PROCEDURAL.value} to create procedural memories."
            )

        if isinstance(messages, str):
            messages = [{"role": "user", "content": messages}]

        elif isinstance(messages, dict):
            messages = [messages]

        elif not isinstance(messages, list):
            raise ValueError("messages must be str, dict, or list[dict]")

        if agent_id is not None and memory_type == MemoryType.PROCEDURAL.value:
            results = self._create_procedural_memory(messages, metadata=processed_metadata, prompt=prompt, timestamp=timestamp)
            return results

        # 处理多模态消息时确保LLM可用
        enable_vision = getattr(self.config.llm.config, 'enable_vision', False)
        vision_details = getattr(self.config.llm.config, 'vision_details', 'auto')
        
        if enable_vision and self.llm is not None:
            messages = parse_vision_messages(messages, self.llm, vision_details)
        else:
            # 即使没有启用视觉或LLM为None，也需要传递LLM实例以处理潜在的图像内容
            messages = parse_vision_messages(messages, self.llm, vision_details)

        with concurrent.futures.ThreadPoolExecutor() as executor:
            future1 = executor.submit(self._add_to_vector_store, messages, processed_metadata, effective_filters, infer, includes, excludes, timestamp, custom_categories)
            future2 = executor.submit(self._add_to_graph, messages, effective_filters)

            concurrent.futures.wait([future1, future2])

            vector_store_result = future1.result()
            graph_result = future2.result()

        if self.api_version == "v1.0":
            warnings.warn(
                "The current add API output format is deprecated. "
                "To use the latest format, set `api_version='v1.1'`. "
                "The current format will be removed in mem0ai 1.1.0 and later versions.",
                category=DeprecationWarning,
                stacklevel=2,
            )
            return vector_store_result

        if self.enable_graph:
            return {
                "results": vector_store_result,
                "relations": graph_result,
            }

        return {"results": vector_store_result}

    def _add_to_vector_store(self, messages, metadata, filters, infer, includes=None, excludes=None, timestamp=None, custom_categories=None):
        if not infer:
            returned_memories = []
            for message_dict in messages:
                if (
                    not isinstance(message_dict, dict)
                    or message_dict.get("role") is None
                    or message_dict.get("content") is None
                ):
                    logger.warning(f"Skipping invalid message format: {message_dict}")
                    continue

                if message_dict["role"] == "system":
                    continue

                per_msg_meta = deepcopy(metadata)
                per_msg_meta["role"] = message_dict["role"]
                
                actor_name = message_dict.get("name")
                if actor_name:
                    per_msg_meta["actor_id"] = actor_name

                msg_content = message_dict["content"]
                
                # Import categorization utility
                from mem0.utils.categorization import categorize_memory_if_enabled
                
                # Get categories (custom or automatic)
                categories = categorize_memory_if_enabled(
                    memory_text=msg_content,
                    llm_instance=self.llm,
                    custom_categories=custom_categories
                )
                
                if categories:
                    per_msg_meta["categories"] = categories

                msg_embeddings = self.embedding_model.embed(msg_content, "add")
                mem_id = self._create_memory(msg_content, msg_embeddings, per_msg_meta, timestamp)

                # Include categories in the response
                memory_response = {
                    "id": mem_id,
                    "memory": msg_content,
                    "event": "ADD",
                    "actor_id": actor_name if actor_name else None,
                    "role": message_dict["role"],
                }
                if categories:
                    memory_response["categories"] = categories
                returned_memories.append(memory_response)
            return returned_memories

        parsed_messages = parse_messages(messages)

        if self.config.custom_fact_extraction_prompt:
            system_prompt = self.config.custom_fact_extraction_prompt
            user_prompt = f"Input:\n{parsed_messages}"
        else:
            system_prompt, user_prompt = get_fact_retrieval_messages(parsed_messages, includes, excludes)

        response = self.llm.generate_response(
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt},
            ],
            response_format={"type": "json_object"},
        )

        try:
            response = remove_code_blocks(response)
            new_retrieved_facts = json.loads(response)["facts"]
        except Exception as e:
            logger.error(f"Error in new_retrieved_facts: {e}")
            new_retrieved_facts = []

        if not new_retrieved_facts:
            logger.debug("No new facts retrieved from input. Skipping memory update LLM call.")

        retrieved_old_memory = []
        new_message_embeddings = {}
        for new_mem in new_retrieved_facts:
            messages_embeddings = self.embedding_model.embed(new_mem, "add")
            new_message_embeddings[new_mem] = messages_embeddings
            existing_memories = self.vector_store.search(
                query=new_mem,
                vectors=messages_embeddings,
                limit=5,
                filters=filters,
            )
            for mem in existing_memories:
                retrieved_old_memory.append({"id": mem.id, "text": mem.payload["data"]})

        unique_data = {}
        for item in retrieved_old_memory:
            unique_data[item["id"]] = item
        retrieved_old_memory = list(unique_data.values())
        logger.info(f"Total existing memories: {len(retrieved_old_memory)}")

        # mapping UUIDs with integers for handling UUID hallucinations
        temp_uuid_mapping = {}
        for idx, item in enumerate(retrieved_old_memory):
            temp_uuid_mapping[str(idx)] = item["id"]
            retrieved_old_memory[idx]["id"] = str(idx)

        if new_retrieved_facts:
            function_calling_prompt = get_update_memory_messages(
                retrieved_old_memory, new_retrieved_facts, self.config.custom_update_memory_prompt
            )

            try:
                response: str = self.llm.generate_response(
                    messages=[{"role": "user", "content": function_calling_prompt}],
                    response_format={"type": "json_object"},
                )
            except Exception as e:
                logger.error(f"Error in new memory actions response: {e}")
                response = ""

            try:
                response = remove_code_blocks(response)
                new_memories_with_actions = json.loads(response)
            except Exception as e:
                logger.error(f"Invalid JSON response: {e}")
                new_memories_with_actions = {}
        else:
            new_memories_with_actions = {}

        returned_memories = []
        try:
            for resp in new_memories_with_actions.get("memory", []):
                logger.info(resp)
                try:
                    action_text = resp.get("text")
                    if not action_text:
                        logger.info("Skipping memory entry because of empty `text` field.")
                        continue

                    event_type = resp.get("event")
                    if event_type == "ADD":
                        # Process categories: custom categories or automatic LLM categorization
                        memory_metadata = deepcopy(metadata)
                        
                        # Import categorization utility
                        from mem0.utils.categorization import categorize_memory_if_enabled
                        
                        # Get categories (custom or automatic)
                        categories = categorize_memory_if_enabled(
                            memory_text=action_text,
                            llm_instance=self.llm,
                            custom_categories=custom_categories
                        )
                        
                        if categories:
                            memory_metadata["categories"] = categories
                            
                        memory_id = self._create_memory(
                            data=action_text,
                            existing_embeddings=new_message_embeddings,
                            metadata=memory_metadata,
                            timestamp=timestamp,
                        )
                        
                        # Include categories in the response
                        memory_response = {"id": memory_id, "memory": action_text, "event": event_type}
                        if categories:
                            memory_response["categories"] = categories
                        returned_memories.append(memory_response)
                    elif event_type == "UPDATE":
                        self._update_memory(
                            memory_id=temp_uuid_mapping[resp.get("id")],
                            data=action_text,
                            existing_embeddings=new_message_embeddings,
                            metadata=deepcopy(metadata),
                        )
                        returned_memories.append(
                            {
                                "id": temp_uuid_mapping[resp.get("id")],
                                "memory": action_text,
                                "event": event_type,
                                "previous_memory": resp.get("old_memory"),
                            }
                        )
                    elif event_type == "DELETE":
                        self._delete_memory(memory_id=temp_uuid_mapping[resp.get("id")])
                        returned_memories.append(
                            {
                                "id": temp_uuid_mapping[resp.get("id")],
                                "memory": action_text,
                                "event": event_type,
                            }
                        )
                    elif event_type == "NONE":
                        logger.info("NOOP for Memory.")
                except Exception as e:
                    logger.error(f"Error processing memory action: {resp}, Error: {e}")
        except Exception as e:
            logger.error(f"Error iterating new_memories_with_actions: {e}")

        keys, encoded_ids = process_telemetry_filters(filters)
        capture_event(
            "mem0.add",
            self,
            {"version": self.api_version, "keys": keys, "encoded_ids": encoded_ids, "sync_type": "sync"},
        )
        return returned_memories

    def _add_to_graph(self, messages, filters):
        added_entities = []
        if self.enable_graph:
            if filters.get("user_id") is None:
                filters["user_id"] = "user"

            data = "\n".join([msg["content"] for msg in messages if "content" in msg and msg["role"] != "system"])
            added_entities = self.graph.add(data, filters)

        return added_entities

    def get(self, memory_id):
        """
        Retrieve a memory by ID.

        Args:
            memory_id (str): ID of the memory to retrieve.

        Returns:
            dict: Retrieved memory.
        """
        capture_event("mem0.get", self, {"memory_id": memory_id, "sync_type": "sync"})
        memory = self.vector_store.get(vector_id=memory_id)
        if not memory:
            return None

        promoted_payload_keys = [
            "user_id",
            "agent_id",
            "run_id",
            "actor_id",
            "role",
            "categories",  # Add categories as a promoted key
        ]

        core_and_promoted_keys = {"data", "hash", "created_at", "updated_at", "id", *promoted_payload_keys}

        result_item = MemoryItem(
            id=memory.id,
            memory=memory.payload["data"],
            hash=memory.payload.get("hash"),
            created_at=memory.payload.get("created_at"),
            updated_at=memory.payload.get("updated_at"),
        ).model_dump()

        for key in promoted_payload_keys:
            if key in memory.payload:
                result_item[key] = memory.payload[key]

        additional_metadata = {k: v for k, v in memory.payload.items() if k not in core_and_promoted_keys}
        if additional_metadata:
            result_item["metadata"] = additional_metadata

        return result_item

    def get_all(
        self,
        *,
        user_id: Optional[str] = None,
        agent_id: Optional[str] = None,
        run_id: Optional[str] = None,
        filters: Optional[Dict[str, Any]] = None,
        limit: int = 100,
    ):
        """
        List all memories.

        Args:
            user_id (str, optional): user id
            agent_id (str, optional): agent id
            run_id (str, optional): run id
            filters (dict, optional): Additional custom key-value filters to apply to the search.
                These are merged with the ID-based scoping filters. For example,
                `filters={"actor_id": "some_user"}`.
            limit (int, optional): The maximum number of memories to return. Defaults to 100.

        Returns:
            dict: A dictionary containing a list of memories under the "results" key,
                  and potentially "relations" if graph store is enabled. For API v1.0,
                  it might return a direct list (see deprecation warning).
                  Example for v1.1+: `{"results": [{"id": "...", "memory": "...", ...}]}`
        """

        _, effective_filters = _build_filters_and_metadata(
            user_id=user_id, agent_id=agent_id, run_id=run_id, input_filters=filters
        )

        if not any(key in effective_filters for key in ("user_id", "agent_id", "run_id")):
            raise ValueError("At least one of 'user_id', 'agent_id', or 'run_id' must be specified.")

        keys, encoded_ids = process_telemetry_filters(effective_filters)
        capture_event(
            "mem0.get_all", self, {"limit": limit, "keys": keys, "encoded_ids": encoded_ids, "sync_type": "sync"}
        )

        with concurrent.futures.ThreadPoolExecutor() as executor:
            future_memories = executor.submit(self._get_all_from_vector_store, effective_filters, limit)
            future_graph_entities = (
                executor.submit(self.graph.get_all, effective_filters, limit) if self.enable_graph else None
            )

            concurrent.futures.wait(
                [future_memories, future_graph_entities] if future_graph_entities else [future_memories]
            )

            all_memories_result = future_memories.result()
            graph_entities_result = future_graph_entities.result() if future_graph_entities else None

        if self.enable_graph:
            return {"results": all_memories_result, "relations": graph_entities_result}

        if self.api_version == "v1.0":
            warnings.warn(
                "The current get_all API output format is deprecated. "
                "To use the latest format, set `api_version='v1.1'` (which returns a dict with a 'results' key). "
                "The current format (direct list for v1.0) will be removed in mem0ai 1.1.0 and later versions.",
                category=DeprecationWarning,
                stacklevel=2,
            )
            return all_memories_result
        else:
            return {"results": all_memories_result}

    def _get_all_from_vector_store(self, filters, limit):
        memories_result = self.vector_store.list(filters=filters, limit=limit)
        actual_memories = (
            memories_result[0]
            if isinstance(memories_result, (tuple, list)) and len(memories_result) > 0
            else memories_result
        )

        promoted_payload_keys = [
            "user_id",
            "agent_id",
            "run_id",
            "actor_id",
            "role",
            "categories",  # Add categories as a promoted key
        ]
        core_and_promoted_keys = {"data", "hash", "created_at", "updated_at", "id", *promoted_payload_keys}

        formatted_memories = []
        for mem in actual_memories:
            memory_item_dict = MemoryItem(
                id=mem.id,
                memory=mem.payload["data"],
                hash=mem.payload.get("hash"),
                created_at=mem.payload.get("created_at"),
                updated_at=mem.payload.get("updated_at"),
            ).model_dump(exclude={"score"})

            for key in promoted_payload_keys:
                if key in mem.payload:
                    memory_item_dict[key] = mem.payload[key]

            additional_metadata = {k: v for k, v in mem.payload.items() if k not in core_and_promoted_keys}
            if additional_metadata:
                memory_item_dict["metadata"] = additional_metadata

            formatted_memories.append(memory_item_dict)

        return formatted_memories

    def search(
        self,
        query: str,
        *,
        user_id: Optional[str] = None,
        agent_id: Optional[str] = None,
        run_id: Optional[str] = None,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
        threshold: Optional[float] = None,
        keyword_search: bool = False,
        rerank: bool = False,
        filter_memories: bool = False,
        retrieval_criteria: Optional[List[Dict[str, Any]]] = None,
    ):
        """
        Searches for memories based on a query
        Args:
            query (str): Query to search for.
            user_id (str, optional): ID of the user to search for. Defaults to None.
            agent_id (str, optional): ID of the agent to search for. Defaults to None.
            run_id (str, optional): ID of the run to search for. Defaults to None.
            limit (int, optional): Limit the number of results. Defaults to 100.
            filters (dict, optional): Filters to apply to the search. Defaults to None..
            threshold (float, optional): Minimum score for a memory to be included in the results. Defaults to None.
            keyword_search (bool, optional): Enable BM25 keyword search for enhanced recall. Defaults to False.
            rerank (bool, optional): Enable LLM-based reranking for improved relevance. Defaults to False.
            filter_memories (bool, optional): Enable intelligent memory filtering for higher precision. Defaults to False.

        Returns:
            dict: A dictionary containing the search results, typically under a "results" key,
                  and potentially "relations" if graph store is enabled.
                  Example for v1.1+: `{"results": [{"id": "...", "memory": "...", "score": 0.8, ...}]}`
        """
        _, effective_filters = _build_filters_and_metadata(
            user_id=user_id, agent_id=agent_id, run_id=run_id, input_filters=filters
        )

        if not any(key in effective_filters for key in ("user_id", "agent_id", "run_id")):
            raise ValueError("At least one of 'user_id', 'agent_id', or 'run_id' must be specified.")

        keys, encoded_ids = process_telemetry_filters(effective_filters)
        capture_event(
            "mem0.search",
            self,
            {
                "limit": limit,
                "version": self.api_version,
                "keys": keys,
                "encoded_ids": encoded_ids,
                "sync_type": "sync",
                "threshold": threshold,
            },
        )

        with concurrent.futures.ThreadPoolExecutor() as executor:
            future_memories = executor.submit(
                self._search_vector_store, query, effective_filters, limit, threshold,
                keyword_search, rerank, filter_memories, retrieval_criteria
            )
            future_graph_entities = (
                executor.submit(self.graph.search, query, effective_filters, limit) if self.enable_graph else None
            )

            concurrent.futures.wait(
                [future_memories, future_graph_entities] if future_graph_entities else [future_memories]
            )

            original_memories = future_memories.result()
            graph_entities = future_graph_entities.result() if future_graph_entities else None

        # 记录SEARCH操作到历史数据库
        try:
            from datetime import datetime
            import pytz
            current_time = datetime.now(pytz.UTC).isoformat()

            # 从filters中提取用户信息
            user_id = effective_filters.get("user_id")
            agent_id = effective_filters.get("agent_id")
            run_id = effective_filters.get("run_id")

            # 生成一个唯一的搜索记录ID
            import uuid
            search_id = str(uuid.uuid4())

            self.db.add_history(
                search_id,  # 使用搜索ID作为memory_id
                None,       # old_memory
                query,      # new_memory存储搜索查询
                "SEARCH",
                created_at=current_time,
                actor_id=user_id or agent_id or run_id,  # 使用可用的ID作为actor_id
                role="user" if user_id else "agent" if agent_id else "system",
                is_deleted=0,
                metadata={
                    "query_type": "semantic",
                    "results_count": len(original_memories),
                    "index_used": "vector",
                    "filters": effective_filters,
                    "limit": limit
                }
            )
        except Exception as e:
            # 记录搜索历史失败不应该影响搜索功能
            logger.warning(f"Failed to record search history: {e}")

        if self.enable_graph:
            return {"results": original_memories, "relations": graph_entities}

        if self.api_version == "v1.0":
            warnings.warn(
                "The current search API output format is deprecated. "
                "To use the latest format, set `api_version='v1.1'`. "
                "The current format will be removed in mem0ai 1.1.0 and later versions.",
                category=DeprecationWarning,
                stacklevel=2,
            )
            return {"results": original_memories}
        else:
            return {"results": original_memories}

    def _search_vector_store(
        self,
        query,
        filters,
        limit,
        threshold: Optional[float] = None,
        keyword_search: bool = False,
        rerank: bool = False,
        filter_memories: bool = False,
        retrieval_criteria: Optional[List[Dict[str, Any]]] = None
    ):
        embeddings = self.embedding_model.embed(query, "search")
        memories = self.vector_store.search(query=query, vectors=embeddings, limit=limit, filters=filters)

        promoted_payload_keys = [
            "user_id",
            "agent_id",
            "run_id",
            "actor_id",
            "role",
            "categories",  # Add categories as a promoted key
        ]

        core_and_promoted_keys = {"data", "hash", "created_at", "updated_at", "id", *promoted_payload_keys}

        original_memories = []
        for mem in memories:
            memory_item_dict = MemoryItem(
                id=mem.id,
                memory=mem.payload["data"],
                hash=mem.payload.get("hash"),
                created_at=mem.payload.get("created_at"),
                updated_at=mem.payload.get("updated_at"),
                score=mem.score,
            ).model_dump()

            for key in promoted_payload_keys:
                if key in mem.payload:
                    memory_item_dict[key] = mem.payload[key]

            additional_metadata = {k: v for k, v in mem.payload.items() if k not in core_and_promoted_keys}
            if additional_metadata:
                memory_item_dict["metadata"] = additional_metadata

            if threshold is None or mem.score >= threshold:
                original_memories.append(memory_item_dict)

        # Check if criteria scoring should be enabled
        criteria_scoring = bool(retrieval_criteria)

        # Apply advanced retrieval if any advanced features are enabled
        if (keyword_search or rerank or filter_memories or criteria_scoring) and AdvancedRetrieval is not None:
            try:
                # Get LLM config if available
                llm_config = {}
                if hasattr(self, 'llm') and self.llm:
                    llm_config = getattr(self.llm, 'config', {})

                # Initialize advanced retrieval
                advanced_retrieval = AdvancedRetrieval(llm_config)

                # Run advanced retrieval synchronously
                import asyncio
                try:
                    # Create a new event loop for async operations
                    enhanced_memories = asyncio.run(advanced_retrieval.search(
                        query, original_memories, keyword_search, rerank, filter_memories,
                        criteria_scoring, retrieval_criteria,
                        threshold=threshold, limit=limit
                    ))
                    return enhanced_memories
                except Exception as e:
                    logging.warning(f"Advanced retrieval failed: {str(e)}, using original results")
                    return original_memories

            except Exception as e:
                logging.error(f"Error in advanced retrieval: {str(e)}")
                return original_memories

        return original_memories

    def update(self, memory_id, data, metadata=None):
        """
        Update a memory by ID.

        Args:
            memory_id (str): ID of the memory to update.
            data (str): Text data to update the memory with.
            metadata (dict, optional): Metadata to update the memory with.

        Returns:
            dict: Updated memory.
        """
        capture_event("mem0.update", self, {"memory_id": memory_id, "sync_type": "sync"})

        existing_embeddings = {data: self.embedding_model.embed(data, "update")}

        self._update_memory(memory_id, data, existing_embeddings, metadata)
        return {"message": "Memory updated successfully!"}

    def delete(self, memory_id):
        """
        Delete a memory by ID.

        Args:
            memory_id (str): ID of the memory to delete.
        """
        capture_event("mem0.delete", self, {"memory_id": memory_id, "sync_type": "sync"})
        self._delete_memory(memory_id)
        return {"message": "Memory deleted successfully!"}

    def delete_all(self, user_id: Optional[str] = None, agent_id: Optional[str] = None, run_id: Optional[str] = None):
        """
        Delete all memories.

        Args:
            user_id (str, optional): ID of the user to delete memories for. Defaults to None.
            agent_id (str, optional): ID of the agent to delete memories for. Defaults to None.
            run_id (str, optional): ID of the run to delete memories for. Defaults to None.
        """
        filters: Dict[str, Any] = {}
        if user_id:
            filters["user_id"] = user_id
        if agent_id:
            filters["agent_id"] = agent_id
        if run_id:
            filters["run_id"] = run_id

        if not filters:
            raise ValueError(
                "At least one filter is required to delete all memories. If you want to delete all memories, use the `reset()` method."
            )

        keys, encoded_ids = process_telemetry_filters(filters)
        capture_event("mem0.delete_all", self, {"keys": keys, "encoded_ids": encoded_ids, "sync_type": "sync"})
        memories = self.vector_store.list(filters=filters)[0]
        for memory in memories:
            self._delete_memory(memory.id)

        logger.info(f"Deleted {len(memories)} memories")

        if self.enable_graph:
            self.graph.delete_all(filters)

        return {"message": "Memories deleted successfully!"}

    def history(self, memory_id):
        """
        Get the history of changes for a memory by ID.

        Args:
            memory_id (str): ID of the memory to get history for.

        Returns:
            list: List of changes for the memory.
        """
        capture_event("mem0.history", self, {"memory_id": memory_id, "sync_type": "sync"})
        return self.db.get_history(memory_id)

    def _create_memory(self, data, existing_embeddings, metadata=None, timestamp=None):
        import time
        start_time = time.time()
        
        logger.debug(f"Creating memory with {data=}")
        if data in existing_embeddings:
            embeddings = existing_embeddings[data]
        else:
            embeddings = self.embedding_model.embed(data, memory_action="add")
        memory_id = str(uuid.uuid4())
        metadata = metadata or {}
        metadata["data"] = data
        metadata["hash"] = hashlib.md5(data.encode()).hexdigest()

        # Use custom timestamp if provided, otherwise use current UTC time
        if timestamp is not None:
            from mem0.utils.timestamp import convert_timestamp_to_utc_datetime, format_timestamp_for_storage
            dt = convert_timestamp_to_utc_datetime(timestamp)
            metadata["created_at"] = format_timestamp_for_storage(dt)
        else:
            metadata["created_at"] = datetime.now(pytz.UTC).isoformat()

        self.vector_store.insert(
            vectors=[embeddings],
            ids=[memory_id],
            payloads=[metadata],
        )
        
        # Calculate response time
        response_time = time.time() - start_time
        
        self.db.add_history(
            memory_id,
            None,
            data,
            "ADD",
            created_at=metadata.get("created_at"),
            actor_id=metadata.get("actor_id"),
            role=self._infer_role_from_metadata(metadata),
            response_time=response_time,
            metadata={
                "memory_type": metadata.get("memory_type", "user"),
                "content_length": len(data) if data else 0,
                "embedding_model": getattr(self.config.embedder.config, 'model', None) if hasattr(self.config.embedder, 'config') else 'unknown',
                "vector_dimension": len(embeddings) if embeddings else 0,
                "user_id": metadata.get("user_id"),
                "agent_id": metadata.get("agent_id"),
                "run_id": metadata.get("run_id"),
                "operation_type": "memory_creation",
                "memory_content": data,
                "content_preview": data[:100] + "..." if data and len(data) > 100 else data
            }
        )
        capture_event("mem0._create_memory", self, {"memory_id": memory_id, "sync_type": "sync"})
        return memory_id

    def _infer_role_from_metadata(self, metadata: Dict[str, Any]) -> str:
        """从metadata推断角色类型"""
        # 如果metadata中直接包含role，则使用它
        if metadata.get("role"):
            return metadata["role"]
        
        # 根据session identifiers推断角色
        if metadata.get("user_id"):
            return "user"
        elif metadata.get("agent_id"):
            return "agent"
        elif metadata.get("run_id"):
            return "system"
        else:
            return "system"  # 默认值
    
    def _infer_role_from_memory_payload(self, payload: Dict[str, Any]) -> str:
        """从memory payload推断角色类型"""
        # 如果payload中直接包含role，则使用它
        if payload.get("role"):
            return payload["role"]
        
        # 根据session identifiers推断角色
        if payload.get("user_id"):
            return "user"
        elif payload.get("agent_id"):
            return "agent"
        elif payload.get("run_id"):
            return "system"
        else:
            return "system"  # 默认值

    def _retrieve_contextual_history(self, filters, limit=10):
        """
        Retrieve historical messages for contextual add v2.

        Args:
            filters (dict): Filters to apply for retrieving historical memories
            limit (int): Maximum number of historical conversations to retrieve (default: 10)

        Returns:
            list: List of historical messages in chronological order
        """
        # Apply performance monitoring if available
        if PerformanceMonitor:
            start_time = time.time()

        # Check cache first for performance optimization
        cache_key = f"{filters.get('user_id', '')}_{filters.get('run_id', '')}_{limit}"
        current_time = time.time()

        if hasattr(self, '_contextual_history_cache') and cache_key in self._contextual_history_cache:
            cached_data = self._contextual_history_cache[cache_key]
            if current_time - cached_data['timestamp'] < self._cache_ttl:
                logging.debug(f"Cache hit for contextual history: {cache_key}")
                return cached_data['data']
            else:
                # Remove expired cache entry
                del self._contextual_history_cache[cache_key]

        try:
            # Get historical memories using existing get_all functionality
            historical_memories = self._get_all_from_vector_store(filters, limit=limit * 5)  # Get more to filter

            # Extract original messages from v2 memories and sort by creation time
            historical_messages = []
            for memory in historical_memories:
                # Check if this memory has original_messages (v2 data)
                if "metadata" in memory and "original_messages" in memory["metadata"]:
                    original_messages = memory["metadata"]["original_messages"]
                    created_at = memory.get("created_at")

                    # Add timestamp to each message for sorting
                    for msg in original_messages:
                        msg_with_timestamp = msg.copy()
                        msg_with_timestamp["_created_at"] = created_at
                        historical_messages.append(msg_with_timestamp)
                elif "metadata" in memory and "api_version" in memory["metadata"] and memory["metadata"]["api_version"] == "v2":
                    # v2 memory but no original_messages (edge case)
                    continue
                # Skip v1 memories as they don't have original_messages

            # Sort by creation time (oldest first for chronological order)
            historical_messages.sort(key=lambda x: x.get("_created_at", ""))

            # Remove timestamp and limit to requested number of messages
            result_messages = []
            for msg in historical_messages[:limit * 2]:  # Allow some buffer for conversation pairs
                clean_msg = {k: v for k, v in msg.items() if k != "_created_at"}
                result_messages.append(clean_msg)

            result = result_messages[:limit * 2]  # Return up to limit*2 messages (user+assistant pairs)

            # Cache the result for future use
            if hasattr(self, '_contextual_history_cache'):
                # Implement simple LRU by removing oldest entries if cache is full
                if len(self._contextual_history_cache) >= self._cache_max_size:
                    oldest_key = min(self._contextual_history_cache.keys(),
                                   key=lambda k: self._contextual_history_cache[k]['timestamp'])
                    del self._contextual_history_cache[oldest_key]

                self._contextual_history_cache[cache_key] = {
                    'data': result,
                    'timestamp': current_time
                }
                logging.debug(f"Cached contextual history for: {cache_key}")

            # Log performance metrics
            if PerformanceMonitor:
                elapsed_ms = (time.time() - start_time) * 1000
                if elapsed_ms > 500:  # Target: <500ms
                    logging.warning(f"Contextual history retrieval exceeded target: {elapsed_ms:.2f}ms > 500ms")
                else:
                    logging.debug(f"Contextual history retrieval completed in {elapsed_ms:.2f}ms")

            return result

        except Exception as e:
            logging.warning(f"Error retrieving contextual history: {e}")
            return []

    def _merge_historical_context(self, historical_messages, new_messages):
        """
        Merge historical messages with new messages to form complete conversation context.

        Args:
            historical_messages (list): List of historical messages from contextual history
            new_messages (list): List of new messages to be added

        Returns:
            list: Merged and deduplicated messages in chronological order
        """
        try:
            # Ensure both inputs are lists
            if not isinstance(historical_messages, list):
                historical_messages = []
            if not isinstance(new_messages, list):
                new_messages = []

            # Create a set to track seen message content for deduplication
            seen_messages = set()
            merged_messages = []

            # Helper function to create a unique key for message deduplication
            def get_message_key(msg):
                if isinstance(msg, dict) and "role" in msg and "content" in msg:
                    # Use role + content hash for deduplication
                    content_hash = hash(str(msg["content"]).strip().lower())
                    return f"{msg['role']}:{content_hash}"
                return None

            # Add historical messages first (they are already sorted chronologically)
            for msg in historical_messages:
                msg_key = get_message_key(msg)
                if msg_key and msg_key not in seen_messages:
                    # Clean message (remove any internal fields)
                    clean_msg = {
                        "role": msg["role"],
                        "content": msg["content"]
                    }
                    # Preserve name field if present
                    if "name" in msg:
                        clean_msg["name"] = msg["name"]

                    merged_messages.append(clean_msg)
                    seen_messages.add(msg_key)

            # Add new messages, avoiding duplicates
            for msg in new_messages:
                msg_key = get_message_key(msg)
                if msg_key and msg_key not in seen_messages:
                    # Clean message (remove any internal fields)
                    clean_msg = {
                        "role": msg["role"],
                        "content": msg["content"]
                    }
                    # Preserve name field if present
                    if "name" in msg:
                        clean_msg["name"] = msg["name"]

                    merged_messages.append(clean_msg)
                    seen_messages.add(msg_key)

            # Implement token length control (rough estimation)
            # Assume average 4 characters per token, limit to ~8000 tokens (~32000 characters)
            max_chars = 32000
            total_chars = 0
            final_messages = []

            for msg in merged_messages:
                msg_chars = len(str(msg.get("content", "")))
                if total_chars + msg_chars <= max_chars:
                    final_messages.append(msg)
                    total_chars += msg_chars
                else:
                    # If we exceed the limit, stop adding more messages
                    break

            logging.info(f"Merged {len(historical_messages)} historical + {len(new_messages)} new messages into {len(final_messages)} unique messages")
            return final_messages

        except Exception as e:
            logging.warning(f"Error merging historical context: {e}")
            # Fallback to just new messages if merging fails
            return new_messages

    def _create_procedural_memory(self, messages, metadata=None, prompt=None, timestamp=None):
        """
        Create a procedural memory

        Args:
            messages (list): List of messages to create a procedural memory from.
            metadata (dict): Metadata to create a procedural memory from.
            prompt (str, optional): Prompt to use for the procedural memory creation. Defaults to None.
        """
        logger.info("Creating procedural memory")

        parsed_messages = [
            {"role": "system", "content": prompt or PROCEDURAL_MEMORY_SYSTEM_PROMPT},
            *messages,
            {
                "role": "user",
                "content": "Create procedural memory of the above conversation.",
            },
        ]

        try:
            procedural_memory = self.llm.generate_response(messages=parsed_messages)
        except Exception as e:
            logger.error(f"Error generating procedural memory summary: {e}")
            raise

        if metadata is None:
            raise ValueError("Metadata cannot be done for procedural memory.")

        metadata["memory_type"] = MemoryType.PROCEDURAL.value
        embeddings = self.embedding_model.embed(procedural_memory, memory_action="add")
        memory_id = self._create_memory(procedural_memory, {procedural_memory: embeddings}, metadata=metadata, timestamp=timestamp)
        capture_event("mem0._create_procedural_memory", self, {"memory_id": memory_id, "sync_type": "sync"})

        result = {"results": [{"id": memory_id, "memory": procedural_memory, "event": "ADD"}]}

        return result

    def _update_memory(self, memory_id, data, existing_embeddings, metadata=None):
        import time
        start_time = time.time()
        
        logger.info(f"Updating memory with {data=}")

        try:
            existing_memory = self.vector_store.get(vector_id=memory_id)
        except Exception:
            logger.error(f"Error getting memory with ID {memory_id} during update.")
            raise ValueError(f"Error getting memory with ID {memory_id}. Please provide a valid 'memory_id'")

        prev_value = existing_memory.payload.get("data")

        new_metadata = deepcopy(metadata) if metadata is not None else {}

        new_metadata["data"] = data
        new_metadata["hash"] = hashlib.md5(data.encode()).hexdigest()
        new_metadata["created_at"] = existing_memory.payload.get("created_at")
        new_metadata["updated_at"] = datetime.now(pytz.timezone("Asia/Shanghai")).isoformat()

        if "user_id" in existing_memory.payload:
            new_metadata["user_id"] = existing_memory.payload["user_id"]
        if "agent_id" in existing_memory.payload:
            new_metadata["agent_id"] = existing_memory.payload["agent_id"]
        if "run_id" in existing_memory.payload:
            new_metadata["run_id"] = existing_memory.payload["run_id"]
        if "actor_id" in existing_memory.payload:
            new_metadata["actor_id"] = existing_memory.payload["actor_id"]
        if "role" in existing_memory.payload:
            new_metadata["role"] = existing_memory.payload["role"]

        if data in existing_embeddings:
            embeddings = existing_embeddings[data]
        else:
            embeddings = self.embedding_model.embed(data, "update")

        self.vector_store.update(
            vector_id=memory_id,
            vector=embeddings,
            payload=new_metadata,
        )
        logger.info(f"Updating memory with ID {memory_id=} with {data=}")

        # Calculate response time
        response_time = time.time() - start_time
        
        self.db.add_history(
            memory_id,
            prev_value,
            data,
            "UPDATE",
            created_at=new_metadata["created_at"],
            updated_at=new_metadata["updated_at"],
            actor_id=new_metadata.get("actor_id"),
            role=self._infer_role_from_metadata(new_metadata),
            response_time=response_time,
            metadata={
                "previous_content_length": len(prev_value) if prev_value else 0,
                "new_content_length": len(data) if data else 0,
                "embedding_model": getattr(self.config.embedder.config, 'model', None) if hasattr(self.config.embedder, 'config') else 'unknown',
                "memory_type": new_metadata.get("memory_type", "user"),
                "user_id": new_metadata.get("user_id"),
                "agent_id": new_metadata.get("agent_id"),
                "run_id": new_metadata.get("run_id"),
                "operation_type": "memory_update",
                "previous_content": prev_value,
                "new_content": data,
                "content_preview": data[:100] + "..." if data and len(data) > 100 else data
            }
        )
        capture_event("mem0._update_memory", self, {"memory_id": memory_id, "sync_type": "sync"})
        return memory_id

    def _delete_memory(self, memory_id):
        import time
        start_time = time.time()
        
        logger.info(f"Deleting memory with {memory_id=}")
        existing_memory = self.vector_store.get(vector_id=memory_id)
        prev_value = existing_memory.payload["data"]
        self.vector_store.delete(vector_id=memory_id)

        # 生成当前时间戳用于DELETE记录
        from datetime import datetime
        import pytz
        current_time = datetime.now(pytz.UTC).isoformat()

        # Calculate response time
        response_time = time.time() - start_time
        
        self.db.add_history(
            memory_id,
            prev_value,
            None,
            "DELETE",
            created_at=current_time,  # 添加时间戳
            actor_id=existing_memory.payload.get("actor_id"),
            role=self._infer_role_from_memory_payload(existing_memory.payload),
            response_time=response_time,
            is_deleted=1,
            metadata={
                "cascade": False,
                "backup_created": True,
                "references_count": existing_memory.payload.get("references_count", 0),
                "memory_type": existing_memory.payload.get("memory_type", "user"),
                "deleted_content_length": len(prev_value) if prev_value else 0,
                "user_id": existing_memory.payload.get("user_id"),
                "agent_id": existing_memory.payload.get("agent_id"),
                "run_id": existing_memory.payload.get("run_id"),
                "operation_type": "memory_deletion",
                "deleted_content": prev_value,
                "content_preview": prev_value[:100] + "..." if prev_value and len(prev_value) > 100 else prev_value
            }
        )
        capture_event("mem0._delete_memory", self, {"memory_id": memory_id, "sync_type": "sync"})
        return memory_id

    def reset(self):
        """
        Reset the memory store by:
            Deletes the vector store collection
            Resets the database
            Recreates the vector store with a new client
        """
        logger.warning("Resetting all memories")

        if hasattr(self.db, "connection") and self.db.connection:
            self.db.connection.execute("DROP TABLE IF EXISTS history")
            self.db.connection.close()

        self.db = SQLiteManager(self.config.history_db_path)
        self.history_manager = HistoryManagerFactory.create_from_existing_storage(self.db)

        if hasattr(self.vector_store, "reset"):
            self.vector_store = VectorStoreFactory.reset(self.vector_store)
        else:
            logger.warning("Vector store does not support reset. Skipping.")
            self.vector_store.delete_col()
            self.vector_store = VectorStoreFactory.create(
                self.config.vector_store.provider, self.config.vector_store.config
            )
        capture_event("mem0.reset", self, {"sync_type": "sync"})

    def chat(self, query):
        raise NotImplementedError("Chat function not implemented yet.")


class AsyncMemory(MemoryBase):
    def __init__(self, config: MemoryConfig = MemoryConfig()):
        self.config = config

        self.embedding_model = EmbedderFactory.create(
            self.config.embedder.provider,
            self.config.embedder.config,
            self.config.vector_store.config,
        )
        self.vector_store = VectorStoreFactory.create(
            self.config.vector_store.provider, self.config.vector_store.config
        )
        self.llm = LlmFactory.create(self.config.llm.provider, self.config.llm.config)
        self.db = SQLiteManager(self.config.history_db_path)
        self.history_manager = HistoryManagerFactory.create_from_existing_storage(self.db)
        self.collection_name = self.config.vector_store.config.collection_name
        self.api_version = self.config.version

        self.enable_graph = False

        if self.config.graph_store.config:
            from mem0.memory.graph_memory import MemoryGraph

            self.graph = MemoryGraph(self.config)
            self.enable_graph = True
        else:
            self.graph = None

        # Initialize performance optimization features
        self._contextual_history_cache = {}  # Cache for historical messages
        self._cache_max_size = 100  # Maximum cache entries
        self._cache_ttl = 300  # Cache TTL in seconds (5 minutes)

        capture_event("mem0.init", self, {"sync_type": "async"})

    @classmethod
    async def from_config(cls, config_dict: Dict[str, Any]):
        try:
            config = cls._process_config(config_dict)
            config = MemoryConfig(**config_dict)
        except ValidationError as e:
            logger.error(f"Configuration validation error: {e}")
            raise
        return cls(config)

    @staticmethod
    def _process_config(config_dict: Dict[str, Any]) -> Dict[str, Any]:
        if "graph_store" in config_dict:
            if "vector_store" not in config_dict and "embedder" in config_dict:
                config_dict["vector_store"] = {}
                config_dict["vector_store"]["config"] = {}
                config_dict["vector_store"]["config"]["embedding_model_dims"] = config_dict["embedder"]["config"][
                    "embedding_dims"
                ]
        try:
            return config_dict
        except ValidationError as e:
            logger.error(f"Configuration validation error: {e}")
            raise

    async def add(
        self,
        messages,
        *,
        user_id: Optional[str] = None,
        agent_id: Optional[str] = None,
        run_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        custom_categories: Optional[List[Dict[str, str]]] = None,
        infer: bool = True,
        memory_type: Optional[str] = None,
        prompt: Optional[str] = None,
        llm=None,
        version: Optional[str] = "v1",
        includes: Optional[str] = None,
        excludes: Optional[str] = None,
        timestamp: Optional[int] = None,
    ):
        """
        Create a new memory asynchronously.

        Args:
            messages (str or List[Dict[str, str]]): Messages to store in the memory.
            user_id (str, optional): ID of the user creating the memory.
            agent_id (str, optional): ID of the agent creating the memory. Defaults to None.
            run_id (str, optional): ID of the run creating the memory. Defaults to None.
            metadata (dict, optional): Metadata to store with the memory. Defaults to None.
            infer (bool, optional): Whether to infer the memories. Defaults to True.
            memory_type (str, optional): Type of memory to create. Defaults to None.
                                         Pass "procedural_memory" to create procedural memories.
            prompt (str, optional): Prompt to use for the memory creation. Defaults to None.
            llm (BaseChatModel, optional): LLM class to use for generating procedural memories. Defaults to None. Useful when user is using LangChain ChatModel.
            version (str, optional): API version for memory creation. "v1" (default) for standard
                behavior, "v2" for contextual add with automatic history retrieval. Defaults to "v1".
            includes (str, optional): Include only specific types of memories. When provided, only
                information related to this topic will be extracted and stored. Defaults to None.
            excludes (str, optional): Exclude specific types of memories. When provided, information
                related to this topic will be ignored during extraction. Defaults to None.
        Returns:
            dict: A dictionary containing the result of the memory addition operation.
        """
        processed_metadata, effective_filters = _build_filters_and_metadata(
            user_id=user_id, agent_id=agent_id, run_id=run_id, input_metadata=metadata
        )

        # Validate version parameter
        if version not in ["v1", "v2"]:
            raise ValueError(f"Invalid version '{version}'. Supported versions: v1, v2")

        # Add version information to metadata for storage
        processed_metadata["api_version"] = version
        if version == "v2":
            # Store original messages for v2 contextual add
            processed_metadata["original_messages"] = messages

        # Version-specific processing
        if version == "v2":
            # Start performance monitoring for v2 processing
            v2_start_time = time.time() if PerformanceMonitor else None

            try:
                # Implement v2 contextual add logic
                import logging
                logging.info("Processing v2 contextual add with automatic history retrieval")

                # Retrieve historical context
                historical_messages = await self._retrieve_contextual_history(effective_filters, limit=10)

                # Merge historical context with new messages
                contextualized_messages = self._merge_historical_context(historical_messages, messages)

                # Use the merged messages for processing instead of original messages
                messages = contextualized_messages

                # Add telemetry event for v2 processing
                capture_event("mem0.contextual_add_v2", self, {
                    "historical_count": len(historical_messages),
                    "new_count": len(processed_metadata.get("original_messages", [])),
                    "merged_count": len(contextualized_messages),
                    "sync_type": "async"
                })

                logging.info(f"v2 contextual add: merged {len(historical_messages)} historical + {len(processed_metadata.get('original_messages', []))} new messages into {len(contextualized_messages)} total messages")

                # Log v2 processing performance
                if PerformanceMonitor and v2_start_time:
                    v2_elapsed_ms = (time.time() - v2_start_time) * 1000
                    if v2_elapsed_ms > 800:  # Target: <800ms
                        logging.warning(f"Async v2 contextual add exceeded target: {v2_elapsed_ms:.2f}ms > 800ms")
                    else:
                        logging.info(f"Async v2 contextual add completed in {v2_elapsed_ms:.2f}ms")

            except Exception as e:
                # Graceful degradation: fall back to v1 behavior on error
                logging.warning(f"v2 contextual add failed, falling back to v1 behavior: {e}")
                capture_event("mem0.contextual_add_v2_fallback", self, {
                    "error": str(e),
                    "sync_type": "async"
                })

        if memory_type is not None and memory_type != MemoryType.PROCEDURAL.value:
            raise ValueError(
                f"Invalid 'memory_type'. Please pass {MemoryType.PROCEDURAL.value} to create procedural memories."
            )

        if isinstance(messages, str):
            messages = [{"role": "user", "content": messages}]

        elif isinstance(messages, dict):
            messages = [messages]

        elif not isinstance(messages, list):
            raise ValueError("messages must be str, dict, or list[dict]")

        if agent_id is not None and memory_type == MemoryType.PROCEDURAL.value:
            results = await self._create_procedural_memory(
                messages, metadata=processed_metadata, prompt=prompt, llm=llm, timestamp=timestamp
            )
            return results

        # 处理多模态消息时确保LLM可用
        enable_vision = getattr(self.config.llm.config, 'enable_vision', False)
        vision_details = getattr(self.config.llm.config, 'vision_details', 'auto')
        
        if enable_vision and self.llm is not None:
            messages = parse_vision_messages(messages, self.llm, vision_details)
        else:
            # 即使没有启用视觉或LLM为None，也需要传递LLM实例以处理潜在的图像内容
            messages = parse_vision_messages(messages, self.llm, vision_details)

        vector_store_task = asyncio.create_task(
            self._add_to_vector_store(messages, processed_metadata, effective_filters, infer, includes, excludes, timestamp)
        )
        graph_task = asyncio.create_task(self._add_to_graph(messages, effective_filters))

        vector_store_result, graph_result = await asyncio.gather(vector_store_task, graph_task)

        if self.api_version == "v1.0":
            warnings.warn(
                "The current add API output format is deprecated. "
                "To use the latest format, set `api_version='v1.1'`. "
                "The current format will be removed in mem0ai 1.1.0 and later versions.",
                category=DeprecationWarning,
                stacklevel=2,
            )
            return vector_store_result

        if self.enable_graph:
            return {
                "results": vector_store_result,
                "relations": graph_result,
            }

        return {"results": vector_store_result}

    async def _add_to_vector_store(
        self,
        messages: list,
        metadata: dict,
        effective_filters: dict,
        infer: bool,
        includes: Optional[str] = None,
        excludes: Optional[str] = None,
        timestamp: Optional[int] = None,
    ):
        if not infer:
            returned_memories = []
            for message_dict in messages:
                if (
                    not isinstance(message_dict, dict)
                    or message_dict.get("role") is None
                    or message_dict.get("content") is None
                ):
                    logger.warning(f"Skipping invalid message format (async): {message_dict}")
                    continue

                if message_dict["role"] == "system":
                    continue

                per_msg_meta = deepcopy(metadata)
                per_msg_meta["role"] = message_dict["role"]

                actor_name = message_dict.get("name")
                if actor_name:
                    per_msg_meta["actor_id"] = actor_name

                msg_content = message_dict["content"]
                msg_embeddings = await asyncio.to_thread(self.embedding_model.embed, msg_content, "add")
                mem_id = await self._create_memory(msg_content, msg_embeddings, per_msg_meta, timestamp)

                returned_memories.append(
                    {
                        "id": mem_id,
                        "memory": msg_content,
                        "event": "ADD",
                        "actor_id": actor_name if actor_name else None,
                        "role": message_dict["role"],
                    }
                )
            return returned_memories

        parsed_messages = parse_messages(messages)
        if self.config.custom_fact_extraction_prompt:
            system_prompt = self.config.custom_fact_extraction_prompt
            user_prompt = f"Input:\n{parsed_messages}"
        else:
            system_prompt, user_prompt = get_fact_retrieval_messages(parsed_messages, includes, excludes)

        response = await asyncio.to_thread(
            self.llm.generate_response,
            messages=[{"role": "system", "content": system_prompt}, {"role": "user", "content": user_prompt}],
            response_format={"type": "json_object"},
        )
        try:
            response = remove_code_blocks(response)
            new_retrieved_facts = json.loads(response)["facts"]
        except Exception as e:
            logger.error(f"Error in new_retrieved_facts: {e}")
            new_retrieved_facts = []

        if not new_retrieved_facts:
            logger.debug("No new facts retrieved from input. Skipping memory update LLM call.")

        retrieved_old_memory = []
        new_message_embeddings = {}

        async def process_fact_for_search(new_mem_content):
            embeddings = await asyncio.to_thread(self.embedding_model.embed, new_mem_content, "add")
            new_message_embeddings[new_mem_content] = embeddings
            existing_mems = await asyncio.to_thread(
                self.vector_store.search,
                query=new_mem_content,
                vectors=embeddings,
                limit=5,
                filters=effective_filters,  # 'filters' is query_filters_for_inference
            )
            return [{"id": mem.id, "text": mem.payload["data"]} for mem in existing_mems]

        search_tasks = [process_fact_for_search(fact) for fact in new_retrieved_facts]
        search_results_list = await asyncio.gather(*search_tasks)
        for result_group in search_results_list:
            retrieved_old_memory.extend(result_group)

        unique_data = {}
        for item in retrieved_old_memory:
            unique_data[item["id"]] = item
        retrieved_old_memory = list(unique_data.values())
        logger.info(f"Total existing memories: {len(retrieved_old_memory)}")
        temp_uuid_mapping = {}
        for idx, item in enumerate(retrieved_old_memory):
            temp_uuid_mapping[str(idx)] = item["id"]
            retrieved_old_memory[idx]["id"] = str(idx)

        if new_retrieved_facts:
            function_calling_prompt = get_update_memory_messages(
                retrieved_old_memory, new_retrieved_facts, self.config.custom_update_memory_prompt
            )
            try:
                response = await asyncio.to_thread(
                    self.llm.generate_response,
                    messages=[{"role": "user", "content": function_calling_prompt}],
                    response_format={"type": "json_object"},
                )
            except Exception as e:
                logger.error(f"Error in new memory actions response: {e}")
                response = ""
            try:
                response = remove_code_blocks(response)
                new_memories_with_actions = json.loads(response)
            except Exception as e:
                logger.error(f"Invalid JSON response: {e}")
                new_memories_with_actions = {}

        returned_memories = []
        try:
            memory_tasks = []
            for resp in new_memories_with_actions.get("memory", []):
                logger.info(resp)
                try:
                    action_text = resp.get("text")
                    if not action_text:
                        continue
                    event_type = resp.get("event")

                    if event_type == "ADD":
                        task = asyncio.create_task(
                            self._create_memory(
                                data=action_text,
                                existing_embeddings=new_message_embeddings,
                                metadata=deepcopy(metadata),
                                timestamp=timestamp,
                            )
                        )
                        memory_tasks.append((task, resp, "ADD", None))
                    elif event_type == "UPDATE":
                        task = asyncio.create_task(
                            self._update_memory(
                                memory_id=temp_uuid_mapping[resp["id"]],
                                data=action_text,
                                existing_embeddings=new_message_embeddings,
                                metadata=deepcopy(metadata),
                            )
                        )
                        memory_tasks.append((task, resp, "UPDATE", temp_uuid_mapping[resp["id"]]))
                    elif event_type == "DELETE":
                        task = asyncio.create_task(self._delete_memory(memory_id=temp_uuid_mapping[resp.get("id")]))
                        memory_tasks.append((task, resp, "DELETE", temp_uuid_mapping[resp.get("id")]))
                    elif event_type == "NONE":
                        logger.info("NOOP for Memory (async).")
                except Exception as e:
                    logger.error(f"Error processing memory action (async): {resp}, Error: {e}")

            for task, resp, event_type, mem_id in memory_tasks:
                try:
                    result_id = await task
                    if event_type == "ADD":
                        returned_memories.append({"id": result_id, "memory": resp.get("text"), "event": event_type})
                    elif event_type == "UPDATE":
                        returned_memories.append(
                            {
                                "id": mem_id,
                                "memory": resp.get("text"),
                                "event": event_type,
                                "previous_memory": resp.get("old_memory"),
                            }
                        )
                    elif event_type == "DELETE":
                        returned_memories.append({"id": mem_id, "memory": resp.get("text"), "event": event_type})
                except Exception as e:
                    logger.error(f"Error awaiting memory task (async): {e}")
        except Exception as e:
            logger.error(f"Error in memory processing loop (async): {e}")

        keys, encoded_ids = process_telemetry_filters(effective_filters)
        capture_event(
            "mem0.add",
            self,
            {"version": self.api_version, "keys": keys, "encoded_ids": encoded_ids, "sync_type": "async"},
        )
        return returned_memories

    async def _add_to_graph(self, messages, filters):
        added_entities = []
        if self.enable_graph:
            if filters.get("user_id") is None:
                filters["user_id"] = "user"

            data = "\n".join([msg["content"] for msg in messages if "content" in msg and msg["role"] != "system"])
            added_entities = await asyncio.to_thread(self.graph.add, data, filters)

        return added_entities

    async def get(self, memory_id):
        """
        Retrieve a memory by ID asynchronously.

        Args:
            memory_id (str): ID of the memory to retrieve.

        Returns:
            dict: Retrieved memory.
        """
        capture_event("mem0.get", self, {"memory_id": memory_id, "sync_type": "async"})
        memory = await asyncio.to_thread(self.vector_store.get, vector_id=memory_id)
        if not memory:
            return None

        promoted_payload_keys = [
            "user_id",
            "agent_id",
            "run_id",
            "actor_id",
            "role",
        ]

        core_and_promoted_keys = {"data", "hash", "created_at", "updated_at", "id", *promoted_payload_keys}

        result_item = MemoryItem(
            id=memory.id,
            memory=memory.payload["data"],
            hash=memory.payload.get("hash"),
            created_at=memory.payload.get("created_at"),
            updated_at=memory.payload.get("updated_at"),
        ).model_dump()

        for key in promoted_payload_keys:
            if key in memory.payload:
                result_item[key] = memory.payload[key]

        additional_metadata = {k: v for k, v in memory.payload.items() if k not in core_and_promoted_keys}
        if additional_metadata:
            result_item["metadata"] = additional_metadata

        return result_item

    async def get_all(
        self,
        *,
        user_id: Optional[str] = None,
        agent_id: Optional[str] = None,
        run_id: Optional[str] = None,
        filters: Optional[Dict[str, Any]] = None,
        limit: int = 100,
    ):
        """
        List all memories.

         Args:
             user_id (str, optional): user id
             agent_id (str, optional): agent id
             run_id (str, optional): run id
             filters (dict, optional): Additional custom key-value filters to apply to the search.
                 These are merged with the ID-based scoping filters. For example,
                 `filters={"actor_id": "some_user"}`.
             limit (int, optional): The maximum number of memories to return. Defaults to 100.

         Returns:
             dict: A dictionary containing a list of memories under the "results" key,
                   and potentially "relations" if graph store is enabled. For API v1.0,
                   it might return a direct list (see deprecation warning).
                   Example for v1.1+: `{"results": [{"id": "...", "memory": "...", ...}]}`
        """

        _, effective_filters = _build_filters_and_metadata(
            user_id=user_id, agent_id=agent_id, run_id=run_id, input_filters=filters
        )

        if not any(key in effective_filters for key in ("user_id", "agent_id", "run_id")):
            raise ValueError(
                "When 'conversation_id' is not provided (classic mode), "
                "at least one of 'user_id', 'agent_id', or 'run_id' must be specified for get_all."
            )

        keys, encoded_ids = process_telemetry_filters(effective_filters)
        capture_event(
            "mem0.get_all", self, {"limit": limit, "keys": keys, "encoded_ids": encoded_ids, "sync_type": "async"}
        )

        with concurrent.futures.ThreadPoolExecutor() as executor:
            future_memories = executor.submit(self._get_all_from_vector_store, effective_filters, limit)
            future_graph_entities = (
                executor.submit(self.graph.get_all, effective_filters, limit) if self.enable_graph else None
            )

            concurrent.futures.wait(
                [future_memories, future_graph_entities] if future_graph_entities else [future_memories]
            )

            all_memories_result = future_memories.result()
            graph_entities_result = future_graph_entities.result() if future_graph_entities else None

        if self.enable_graph:
            return {"results": all_memories_result, "relations": graph_entities_result}

        if self.api_version == "v1.0":
            warnings.warn(
                "The current get_all API output format is deprecated. "
                "To use the latest format, set `api_version='v1.1'` (which returns a dict with a 'results' key). "
                "The current format (direct list for v1.0) will be removed in mem0ai 1.1.0 and later versions.",
                category=DeprecationWarning,
                stacklevel=2,
            )
            return all_memories_result
        else:
            return {"results": all_memories_result}

    async def _get_all_from_vector_store(self, filters, limit):
        memories_result = await asyncio.to_thread(self.vector_store.list, filters=filters, limit=limit)
        actual_memories = (
            memories_result[0]
            if isinstance(memories_result, (tuple, list)) and len(memories_result) > 0
            else memories_result
        )

        promoted_payload_keys = [
            "user_id",
            "agent_id",
            "run_id",
            "actor_id",
            "role",
        ]
        core_and_promoted_keys = {"data", "hash", "created_at", "updated_at", "id", *promoted_payload_keys}

        formatted_memories = []
        for mem in actual_memories:
            memory_item_dict = MemoryItem(
                id=mem.id,
                memory=mem.payload["data"],
                hash=mem.payload.get("hash"),
                created_at=mem.payload.get("created_at"),
                updated_at=mem.payload.get("updated_at"),
            ).model_dump(exclude={"score"})

            for key in promoted_payload_keys:
                if key in mem.payload:
                    memory_item_dict[key] = mem.payload[key]

            additional_metadata = {k: v for k, v in mem.payload.items() if k not in core_and_promoted_keys}
            if additional_metadata:
                memory_item_dict["metadata"] = additional_metadata

            formatted_memories.append(memory_item_dict)

        return formatted_memories

    async def search(
        self,
        query: str,
        *,
        user_id: Optional[str] = None,
        agent_id: Optional[str] = None,
        run_id: Optional[str] = None,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
        threshold: Optional[float] = None,
        keyword_search: bool = False,
        rerank: bool = False,
        filter_memories: bool = False,
        retrieval_criteria: Optional[List[Dict[str, Any]]] = None,
    ):
        """
        Searches for memories based on a query
        Args:
            query (str): Query to search for.
            user_id (str, optional): ID of the user to search for. Defaults to None.
            agent_id (str, optional): ID of the agent to search for. Defaults to None.
            run_id (str, optional): ID of the run to search for. Defaults to None.
            limit (int, optional): Limit the number of results. Defaults to 100.
            filters (dict, optional): Filters to apply to the search. Defaults to None.
            threshold (float, optional): Minimum score for a memory to be included in the results. Defaults to None.
            keyword_search (bool, optional): Enable BM25 keyword search for enhanced recall. Defaults to False.
            rerank (bool, optional): Enable LLM-based reranking for improved relevance. Defaults to False.
            filter_memories (bool, optional): Enable intelligent memory filtering for higher precision. Defaults to False.

        Returns:
            dict: A dictionary containing the search results, typically under a "results" key,
                  and potentially "relations" if graph store is enabled.
                  Example for v1.1+: `{"results": [{"id": "...", "memory": "...", "score": 0.8, ...}]}`
        """

        _, effective_filters = _build_filters_and_metadata(
            user_id=user_id, agent_id=agent_id, run_id=run_id, input_filters=filters
        )

        if not any(key in effective_filters for key in ("user_id", "agent_id", "run_id")):
            raise ValueError("at least one of 'user_id', 'agent_id', or 'run_id' must be specified ")

        keys, encoded_ids = process_telemetry_filters(effective_filters)
        capture_event(
            "mem0.search",
            self,
            {
                "limit": limit,
                "version": self.api_version,
                "keys": keys,
                "encoded_ids": encoded_ids,
                "sync_type": "async",
                "threshold": threshold,
            },
        )

        vector_store_task = asyncio.create_task(
            self._search_vector_store(query, effective_filters, limit, threshold, keyword_search, rerank, filter_memories, retrieval_criteria)
        )

        graph_task = None
        if self.enable_graph:
            if hasattr(self.graph.search, "__await__"):  # Check if graph search is async
                graph_task = asyncio.create_task(self.graph.search(query, effective_filters, limit))
            else:
                graph_task = asyncio.create_task(asyncio.to_thread(self.graph.search, query, effective_filters, limit))

        if graph_task:
            original_memories, graph_entities = await asyncio.gather(vector_store_task, graph_task)
        else:
            original_memories = await vector_store_task
            graph_entities = None

        # 记录SEARCH操作到历史数据库（异步版本）
        try:
            from datetime import datetime
            import pytz
            current_time = datetime.now(pytz.UTC).isoformat()

            # 从filters中提取用户信息
            user_id = effective_filters.get("user_id")
            agent_id = effective_filters.get("agent_id")
            run_id = effective_filters.get("run_id")

            # 生成一个唯一的搜索记录ID
            import uuid
            search_id = str(uuid.uuid4())

            await asyncio.to_thread(
                self.db.add_history,
                search_id,  # 使用搜索ID作为memory_id
                None,       # old_memory
                query,      # new_memory存储搜索查询
                "SEARCH",
                created_at=current_time,
                actor_id=user_id or agent_id or run_id,  # 使用可用的ID作为actor_id
                role="user" if user_id else "agent" if agent_id else "system",
                is_deleted=0,
                metadata={
                    "query_type": "semantic",
                    "results_count": len(original_memories),
                    "index_used": "vector",
                    "filters": effective_filters,
                    "limit": limit,
                    "async_operation": True
                }
            )
        except Exception as e:
            # 记录搜索历史失败不应该影响搜索功能
            logger.warning(f"Failed to record search history: {e}")

        if self.enable_graph:
            return {"results": original_memories, "relations": graph_entities}

        if self.api_version == "v1.0":
            warnings.warn(
                "The current search API output format is deprecated. "
                "To use the latest format, set `api_version='v1.1'`. "
                "The current format will be removed in mem0ai 1.1.0 and later versions.",
                category=DeprecationWarning,
                stacklevel=2,
            )
            return {"results": original_memories}
        else:
            return {"results": original_memories}

    async def _search_vector_store(
        self,
        query,
        filters,
        limit,
        threshold: Optional[float] = None,
        keyword_search: bool = False,
        rerank: bool = False,
        filter_memories: bool = False,
        retrieval_criteria: Optional[List[Dict[str, Any]]] = None
    ):
        embeddings = await asyncio.to_thread(self.embedding_model.embed, query, "search")
        memories = await asyncio.to_thread(
            self.vector_store.search, query=query, vectors=embeddings, limit=limit, filters=filters
        )

        promoted_payload_keys = [
            "user_id",
            "agent_id",
            "run_id",
            "actor_id",
            "role",
        ]

        core_and_promoted_keys = {"data", "hash", "created_at", "updated_at", "id", *promoted_payload_keys}

        original_memories = []
        for mem in memories:
            memory_item_dict = MemoryItem(
                id=mem.id,
                memory=mem.payload["data"],
                hash=mem.payload.get("hash"),
                created_at=mem.payload.get("created_at"),
                updated_at=mem.payload.get("updated_at"),
                score=mem.score,
            ).model_dump()

            for key in promoted_payload_keys:
                if key in mem.payload:
                    memory_item_dict[key] = mem.payload[key]

            additional_metadata = {k: v for k, v in mem.payload.items() if k not in core_and_promoted_keys}
            if additional_metadata:
                memory_item_dict["metadata"] = additional_metadata

            if threshold is None or mem.score >= threshold:
                original_memories.append(memory_item_dict)

        # Check if criteria scoring should be enabled
        criteria_scoring = bool(retrieval_criteria)

        # Apply advanced retrieval if any advanced features are enabled
        if (keyword_search or rerank or filter_memories or criteria_scoring) and AdvancedRetrieval is not None:
            try:
                # Get LLM config if available
                llm_config = {}
                if hasattr(self, 'llm') and self.llm:
                    llm_config = getattr(self.llm, 'config', {})

                # Initialize advanced retrieval
                advanced_retrieval = AdvancedRetrieval(llm_config)

                # Run advanced retrieval asynchronously
                enhanced_memories = await advanced_retrieval.search(
                    query, original_memories, keyword_search, rerank, filter_memories,
                    criteria_scoring, retrieval_criteria,
                    threshold=threshold, limit=limit
                )
                return enhanced_memories

            except Exception as e:
                logging.error(f"Error in advanced retrieval: {str(e)}")
                return original_memories

        return original_memories

    async def update(self, memory_id, data, metadata=None):
        """
        Update a memory by ID asynchronously.

        Args:
            memory_id (str): ID of the memory to update.
            data (str): Text data to update the memory with.
            metadata (dict, optional): Metadata to update the memory with.

        Returns:
            dict: Updated memory.
        """
        capture_event("mem0.update", self, {"memory_id": memory_id, "sync_type": "async"})

        embeddings = await asyncio.to_thread(self.embedding_model.embed, data, "update")
        existing_embeddings = {data: embeddings}

        await self._update_memory(memory_id, data, existing_embeddings, metadata)
        return {"message": "Memory updated successfully!"}

    async def delete(self, memory_id):
        """
        Delete a memory by ID asynchronously.

        Args:
            memory_id (str): ID of the memory to delete.
        """
        capture_event("mem0.delete", self, {"memory_id": memory_id, "sync_type": "async"})
        await self._delete_memory(memory_id)
        return {"message": "Memory deleted successfully!"}

    async def delete_all(self, user_id=None, agent_id=None, run_id=None):
        """
        Delete all memories asynchronously.

        Args:
            user_id (str, optional): ID of the user to delete memories for. Defaults to None.
            agent_id (str, optional): ID of the agent to delete memories for. Defaults to None.
            run_id (str, optional): ID of the run to delete memories for. Defaults to None.
        """
        filters = {}
        if user_id:
            filters["user_id"] = user_id
        if agent_id:
            filters["agent_id"] = agent_id
        if run_id:
            filters["run_id"] = run_id

        if not filters:
            raise ValueError(
                "At least one filter is required to delete all memories. If you want to delete all memories, use the `reset()` method."
            )

        keys, encoded_ids = process_telemetry_filters(filters)
        capture_event("mem0.delete_all", self, {"keys": keys, "encoded_ids": encoded_ids, "sync_type": "async"})
        memories = await asyncio.to_thread(self.vector_store.list, filters=filters)

        delete_tasks = []
        for memory in memories[0]:
            delete_tasks.append(self._delete_memory(memory.id))

        await asyncio.gather(*delete_tasks)

        logger.info(f"Deleted {len(memories[0])} memories")

        if self.enable_graph:
            await asyncio.to_thread(self.graph.delete_all, filters)

        return {"message": "Memories deleted successfully!"}

    async def history(self, memory_id):
        """
        Get the history of changes for a memory by ID asynchronously.

        Args:
            memory_id (str): ID of the memory to get history for.

        Returns:
            list: List of changes for the memory.
        """
        capture_event("mem0.history", self, {"memory_id": memory_id, "sync_type": "async"})
        return await asyncio.to_thread(self.db.get_history, memory_id)

    async def _create_memory(self, data, existing_embeddings, metadata=None, timestamp=None):
        import time
        start_time = time.time()
        
        logger.debug(f"Creating memory with {data=}")
        if data in existing_embeddings:
            embeddings = existing_embeddings[data]
        else:
            embeddings = await asyncio.to_thread(self.embedding_model.embed, data, memory_action="add")

        memory_id = str(uuid.uuid4())
        metadata = metadata or {}
        metadata["data"] = data
        metadata["hash"] = hashlib.md5(data.encode()).hexdigest()

        # Use custom timestamp if provided, otherwise use current UTC time
        if timestamp is not None:
            from mem0.utils.timestamp import convert_timestamp_to_utc_datetime, format_timestamp_for_storage
            dt = convert_timestamp_to_utc_datetime(timestamp)
            metadata["created_at"] = format_timestamp_for_storage(dt)
        else:
            metadata["created_at"] = datetime.now(pytz.UTC).isoformat()

        await asyncio.to_thread(
            self.vector_store.insert,
            vectors=[embeddings],
            ids=[memory_id],
            payloads=[metadata],
        )

        # Calculate response time
        response_time = time.time() - start_time

        await asyncio.to_thread(
            self.db.add_history,
            memory_id,
            None,
            data,
            "ADD",
            created_at=metadata.get("created_at"),
            actor_id=metadata.get("actor_id"),
            role=self._infer_role_from_metadata(metadata),
            response_time=response_time,
            metadata={
                "memory_type": metadata.get("memory_type", "user"),
                "content_length": len(data) if data else 0,
                "embedding_model": getattr(self.config.embedder.config, 'model', None) if hasattr(self.config.embedder, 'config') else 'unknown',
                "vector_dimension": len(embeddings) if embeddings else 0,
                "user_id": metadata.get("user_id"),
                "agent_id": metadata.get("agent_id"),
                "run_id": metadata.get("run_id"),
                "operation_type": "memory_creation",
                "async_operation": True,
                "memory_content": data,
                "content_preview": data[:100] + "..." if data and len(data) > 100 else data
            }
        )

        capture_event("mem0._create_memory", self, {"memory_id": memory_id, "sync_type": "async"})
        return memory_id

    async def _retrieve_contextual_history(self, filters, limit=10):
        """
        Retrieve historical messages for contextual add v2 asynchronously.

        Args:
            filters (dict): Filters to apply for retrieving historical memories
            limit (int): Maximum number of historical conversations to retrieve (default: 10)

        Returns:
            list: List of historical messages in chronological order
        """
        # Apply performance monitoring if available
        if PerformanceMonitor:
            start_time = time.time()

        # Check cache first for performance optimization
        cache_key = f"{filters.get('user_id', '')}_{filters.get('run_id', '')}_{limit}"
        current_time = time.time()

        if hasattr(self, '_contextual_history_cache') and cache_key in self._contextual_history_cache:
            cached_data = self._contextual_history_cache[cache_key]
            if current_time - cached_data['timestamp'] < self._cache_ttl:
                logging.debug(f"Async cache hit for contextual history: {cache_key}")
                return cached_data['data']
            else:
                # Remove expired cache entry
                del self._contextual_history_cache[cache_key]

        try:
            # Get historical memories using existing get_all functionality
            historical_memories = await asyncio.to_thread(
                self._get_all_from_vector_store, filters, limit * 5
            )  # Get more to filter

            # Extract original messages from v2 memories and sort by creation time
            historical_messages = []
            for memory in historical_memories:
                # Check if this memory has original_messages (v2 data)
                if "metadata" in memory and "original_messages" in memory["metadata"]:
                    original_messages = memory["metadata"]["original_messages"]
                    created_at = memory.get("created_at")

                    # Add timestamp to each message for sorting
                    for msg in original_messages:
                        msg_with_timestamp = msg.copy()
                        msg_with_timestamp["_created_at"] = created_at
                        historical_messages.append(msg_with_timestamp)
                elif "metadata" in memory and "api_version" in memory["metadata"] and memory["metadata"]["api_version"] == "v2":
                    # v2 memory but no original_messages (edge case)
                    continue
                # Skip v1 memories as they don't have original_messages

            # Sort by creation time (oldest first for chronological order)
            historical_messages.sort(key=lambda x: x.get("_created_at", ""))

            # Remove timestamp and limit to requested number of messages
            result_messages = []
            for msg in historical_messages[:limit * 2]:  # Allow some buffer for conversation pairs
                clean_msg = {k: v for k, v in msg.items() if k != "_created_at"}
                result_messages.append(clean_msg)

            result = result_messages[:limit * 2]  # Return up to limit*2 messages (user+assistant pairs)

            # Cache the result for future use
            if hasattr(self, '_contextual_history_cache'):
                # Implement simple LRU by removing oldest entries if cache is full
                if len(self._contextual_history_cache) >= self._cache_max_size:
                    oldest_key = min(self._contextual_history_cache.keys(),
                                   key=lambda k: self._contextual_history_cache[k]['timestamp'])
                    del self._contextual_history_cache[oldest_key]

                self._contextual_history_cache[cache_key] = {
                    'data': result,
                    'timestamp': current_time
                }
                logging.debug(f"Async cached contextual history for: {cache_key}")

            # Log performance metrics
            if PerformanceMonitor:
                elapsed_ms = (time.time() - start_time) * 1000
                if elapsed_ms > 500:  # Target: <500ms
                    logging.warning(f"Async contextual history retrieval exceeded target: {elapsed_ms:.2f}ms > 500ms")
                else:
                    logging.debug(f"Async contextual history retrieval completed in {elapsed_ms:.2f}ms")

            return result

        except Exception as e:
            logging.warning(f"Error retrieving contextual history: {e}")
            return []

    def _merge_historical_context(self, historical_messages, new_messages):
        """
        Merge historical messages with new messages to form complete conversation context.

        Args:
            historical_messages (list): List of historical messages from contextual history
            new_messages (list): List of new messages to be added

        Returns:
            list: Merged and deduplicated messages in chronological order
        """
        try:
            # Ensure both inputs are lists
            if not isinstance(historical_messages, list):
                historical_messages = []
            if not isinstance(new_messages, list):
                new_messages = []

            # Create a set to track seen message content for deduplication
            seen_messages = set()
            merged_messages = []

            # Helper function to create a unique key for message deduplication
            def get_message_key(msg):
                if isinstance(msg, dict) and "role" in msg and "content" in msg:
                    # Use role + content hash for deduplication
                    content_hash = hash(str(msg["content"]).strip().lower())
                    return f"{msg['role']}:{content_hash}"
                return None

            # Add historical messages first (they are already sorted chronologically)
            for msg in historical_messages:
                msg_key = get_message_key(msg)
                if msg_key and msg_key not in seen_messages:
                    # Clean message (remove any internal fields)
                    clean_msg = {
                        "role": msg["role"],
                        "content": msg["content"]
                    }
                    # Preserve name field if present
                    if "name" in msg:
                        clean_msg["name"] = msg["name"]

                    merged_messages.append(clean_msg)
                    seen_messages.add(msg_key)

            # Add new messages, avoiding duplicates
            for msg in new_messages:
                msg_key = get_message_key(msg)
                if msg_key and msg_key not in seen_messages:
                    # Clean message (remove any internal fields)
                    clean_msg = {
                        "role": msg["role"],
                        "content": msg["content"]
                    }
                    # Preserve name field if present
                    if "name" in msg:
                        clean_msg["name"] = msg["name"]

                    merged_messages.append(clean_msg)
                    seen_messages.add(msg_key)

            # Implement token length control (rough estimation)
            # Assume average 4 characters per token, limit to ~8000 tokens (~32000 characters)
            max_chars = 32000
            total_chars = 0
            final_messages = []

            for msg in merged_messages:
                msg_chars = len(str(msg.get("content", "")))
                if total_chars + msg_chars <= max_chars:
                    final_messages.append(msg)
                    total_chars += msg_chars
                else:
                    # If we exceed the limit, stop adding more messages
                    break

            logging.info(f"Merged {len(historical_messages)} historical + {len(new_messages)} new messages into {len(final_messages)} unique messages")
            return final_messages

        except Exception as e:
            logging.warning(f"Error merging historical context: {e}")
            # Fallback to just new messages if merging fails
            return new_messages

    async def _create_procedural_memory(self, messages, metadata=None, llm=None, prompt=None, timestamp=None):
        """
        Create a procedural memory asynchronously

        Args:
            messages (list): List of messages to create a procedural memory from.
            metadata (dict): Metadata to create a procedural memory from.
            llm (llm, optional): LLM to use for the procedural memory creation. Defaults to None.
            prompt (str, optional): Prompt to use for the procedural memory creation. Defaults to None.
        """
        try:
            from langchain_core.messages.utils import (
                convert_to_messages,  # type: ignore
            )
        except Exception:
            logger.error(
                "Import error while loading langchain-core. Please install 'langchain-core' to use procedural memory."
            )
            raise

        logger.info("Creating procedural memory")

        parsed_messages = [
            {"role": "system", "content": prompt or PROCEDURAL_MEMORY_SYSTEM_PROMPT},
            *messages,
            {"role": "user", "content": "Create procedural memory of the above conversation."},
        ]

        try:
            if llm is not None:
                parsed_messages = convert_to_messages(parsed_messages)
                response = await asyncio.to_thread(llm.invoke, input=parsed_messages)
                procedural_memory = response.content
            else:
                procedural_memory = await asyncio.to_thread(self.llm.generate_response, messages=parsed_messages)
        except Exception as e:
            logger.error(f"Error generating procedural memory summary: {e}")
            raise

        if metadata is None:
            raise ValueError("Metadata cannot be done for procedural memory.")

        metadata["memory_type"] = MemoryType.PROCEDURAL.value
        embeddings = await asyncio.to_thread(self.embedding_model.embed, procedural_memory, memory_action="add")
        memory_id = await self._create_memory(procedural_memory, {procedural_memory: embeddings}, metadata=metadata, timestamp=timestamp)
        capture_event("mem0._create_procedural_memory", self, {"memory_id": memory_id, "sync_type": "async"})

        result = {"results": [{"id": memory_id, "memory": procedural_memory, "event": "ADD"}]}

        return result

    async def _update_memory(self, memory_id, data, existing_embeddings, metadata=None):
        logger.info(f"Updating memory with {data=}")

        try:
            existing_memory = await asyncio.to_thread(self.vector_store.get, vector_id=memory_id)
        except Exception:
            logger.error(f"Error getting memory with ID {memory_id} during update.")
            raise ValueError(f"Error getting memory with ID {memory_id}. Please provide a valid 'memory_id'")

        prev_value = existing_memory.payload.get("data")

        new_metadata = deepcopy(metadata) if metadata is not None else {}

        new_metadata["data"] = data
        new_metadata["hash"] = hashlib.md5(data.encode()).hexdigest()
        new_metadata["created_at"] = existing_memory.payload.get("created_at")
        new_metadata["updated_at"] = datetime.now(pytz.timezone("Asia/Shanghai")).isoformat()

        if "user_id" in existing_memory.payload:
            new_metadata["user_id"] = existing_memory.payload["user_id"]
        if "agent_id" in existing_memory.payload:
            new_metadata["agent_id"] = existing_memory.payload["agent_id"]
        if "run_id" in existing_memory.payload:
            new_metadata["run_id"] = existing_memory.payload["run_id"]

        if "actor_id" in existing_memory.payload:
            new_metadata["actor_id"] = existing_memory.payload["actor_id"]
        if "role" in existing_memory.payload:
            new_metadata["role"] = existing_memory.payload["role"]

        if data in existing_embeddings:
            embeddings = existing_embeddings[data]
        else:
            embeddings = await asyncio.to_thread(self.embedding_model.embed, data, "update")

        await asyncio.to_thread(
            self.vector_store.update,
            vector_id=memory_id,
            vector=embeddings,
            payload=new_metadata,
        )
        logger.info(f"Updating memory with ID {memory_id=} with {data=}")

        await asyncio.to_thread(
            self.db.add_history,
            memory_id,
            prev_value,
            data,
            "UPDATE",
            created_at=new_metadata["created_at"],
            updated_at=new_metadata["updated_at"],
            actor_id=new_metadata.get("actor_id"),
            role=self._infer_role_from_metadata(new_metadata),
            metadata={
                "previous_content_length": len(prev_value) if prev_value else 0,
                "new_content_length": len(data) if data else 0,
                "embedding_model": getattr(self.config.embedder.config, 'model', None) if hasattr(self.config.embedder, 'config') else 'unknown',
                "memory_type": new_metadata.get("memory_type", "user"),
                "async_operation": True
            }
        )
        capture_event("mem0._update_memory", self, {"memory_id": memory_id, "sync_type": "async"})
        return memory_id

    async def _delete_memory(self, memory_id):
        logger.info(f"Deleting memory with {memory_id=}")
        existing_memory = await asyncio.to_thread(self.vector_store.get, vector_id=memory_id)
        prev_value = existing_memory.payload["data"]

        await asyncio.to_thread(self.vector_store.delete, vector_id=memory_id)

        # 生成当前时间戳用于DELETE记录
        from datetime import datetime
        import pytz
        current_time = datetime.now(pytz.UTC).isoformat()

        await asyncio.to_thread(
            self.db.add_history,
            memory_id,
            prev_value,
            None,
            "DELETE",
            created_at=current_time,  # 添加时间戳
            actor_id=existing_memory.payload.get("actor_id"),
            role=self._infer_role_from_memory_payload(existing_memory.payload),
            is_deleted=1,
            metadata={
                "cascade": False,
                "backup_created": True,
                "references_count": existing_memory.payload.get("references_count", 0),
                "memory_type": existing_memory.payload.get("memory_type", "user"),
                "deleted_content_length": len(prev_value) if prev_value else 0,
                "async_operation": True
            }
        )

        capture_event("mem0._delete_memory", self, {"memory_id": memory_id, "sync_type": "async"})
        return memory_id

    async def reset(self):
        """
        Reset the memory store asynchronously by:
            Deletes the vector store collection
            Resets the database
            Recreates the vector store with a new client
        """
        logger.warning("Resetting all memories")
        await asyncio.to_thread(self.vector_store.delete_col)

        gc.collect()

        if hasattr(self.vector_store, "client") and hasattr(self.vector_store.client, "close"):
            await asyncio.to_thread(self.vector_store.client.close)

        if hasattr(self.db, "connection") and self.db.connection:
            await asyncio.to_thread(lambda: self.db.connection.execute("DROP TABLE IF EXISTS history"))
            await asyncio.to_thread(self.db.connection.close)

        self.db = SQLiteManager(self.config.history_db_path)
        self.history_manager = HistoryManagerFactory.create_from_existing_storage(self.db)

        self.vector_store = VectorStoreFactory.create(
            self.config.vector_store.provider, self.config.vector_store.config
        )
        capture_event("mem0.reset", self, {"sync_type": "async"})

    async def chat(self, query):
        raise NotImplementedError("Chat function not implemented yet.")
