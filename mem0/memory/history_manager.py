"""
History Manager for Mem0 Memory System

实现单一职责原则(SRP)的历史记录管理类，负责所有历史记录相关的操作。
遵循SOLID原则设计，提供一致的历史记录管理接口。
"""

import logging
import json
from datetime import datetime
from typing import Dict, Any, List, Optional, Union
from enum import Enum

from .storage import SQLiteManager

logger = logging.getLogger(__name__)


class HistoryEventType(Enum):
    """历史事件类型枚举"""
    ADD = "ADD"
    UPDATE = "UPDATE"
    DELETE = "DELETE"
    SEARCH = "SEARCH"


class HistoryRole(Enum):
    """历史记录角色枚举"""
    USER = "user"
    AGENT = "agent"
    SYSTEM = "system"


class HistoryMetadataBuilder:
    """
    历史元数据构建器
    实现建造者模式，简化元数据创建过程
    """
    
    def __init__(self):
        self.metadata = {}
    
    def set_operation_type(self, operation_type: str) -> 'HistoryMetadataBuilder':
        """设置操作类型"""
        self.metadata["operation_type"] = operation_type
        return self
    
    def set_content_info(self, content_length: int, embedding_model: str = None) -> 'HistoryMetadataBuilder':
        """设置内容信息"""
        self.metadata["content_length"] = content_length
        if embedding_model:
            self.metadata["embedding_model"] = embedding_model
        return self
    
    def set_memory_type(self, memory_type: str) -> 'HistoryMetadataBuilder':
        """设置记忆类型"""
        self.metadata["memory_type"] = memory_type  
        return self
    
    def set_performance_info(self, response_time: float = None, async_operation: bool = False) -> 'HistoryMetadataBuilder':
        """设置性能信息"""
        if response_time is not None:
            self.metadata["response_time"] = response_time
        self.metadata["async_operation"] = async_operation
        return self
    
    def set_search_info(self, query_type: str, results_count: int, filters: Dict = None, limit: int = None) -> 'HistoryMetadataBuilder':
        """设置搜索信息"""
        self.metadata.update({
            "query_type": query_type,
            "results_count": results_count,
        })
        if filters:
            self.metadata["filters"] = filters
        if limit:
            self.metadata["limit"] = limit
        return self
    
    def set_delete_info(self, cascade: bool = False, backup_created: bool = True, references_count: int = 0) -> 'HistoryMetadataBuilder':
        """设置删除信息"""
        self.metadata.update({
            "cascade": cascade,
            "backup_created": backup_created,
            "references_count": references_count
        })
        return self
    
    def set_update_info(self, previous_content_length: int, new_content_length: int) -> 'HistoryMetadataBuilder':
        """设置更新信息"""
        self.metadata.update({
            "previous_content_length": previous_content_length,
            "new_content_length": new_content_length
        })
        return self
    
    def add_custom_field(self, key: str, value: Any) -> 'HistoryMetadataBuilder':
        """添加自定义字段"""
        self.metadata[key] = value
        return self
    
    def build(self) -> Dict[str, Any]:
        """构建最终的元数据字典"""
        return self.metadata.copy()


class HistoryManager:
    """
    历史记录管理器
    
    实现单一职责原则(SRP)，专门负责历史记录的管理
    提供一致的接口和完整的验证机制
    """
    
    def __init__(self, storage_manager: SQLiteManager):
        """
        初始化历史记录管理器
        
        Args:
            storage_manager: SQLite存储管理器实例
        """
        self.storage = storage_manager
        self._default_role = HistoryRole.SYSTEM.value
        
    def record_add_memory(
        self,
        memory_id: str,
        content: str,
        actor_id: Optional[str] = None,
        role: Optional[str] = None,
        memory_type: str = "user",
        embedding_model: str = "unknown",
        vector_dimension: int = 0,
        created_at: Optional[str] = None,
        is_async: bool = False
    ) -> str:
        """
        记录添加内存操作
        
        Args:
            memory_id: 内存记录ID
            content: 内存内容
            actor_id: 操作者ID
            role: 角色
            memory_type: 内存类型
            embedding_model: 嵌入模型名称
            vector_dimension: 向量维度
            created_at: 创建时间
            is_async: 是否异步操作
            
        Returns:
            str: 历史记录ID
        """
        metadata = (HistoryMetadataBuilder()
                   .set_operation_type("memory_creation")
                   .set_content_info(len(content) if content else 0, embedding_model)
                   .set_memory_type(memory_type)
                   .set_performance_info(async_operation=is_async)
                   .add_custom_field("vector_dimension", vector_dimension)
                   .build())
                   
        return self._add_history_record(
            memory_id=memory_id,
            old_memory=None,
            new_memory=content,
            event=HistoryEventType.ADD.value,
            actor_id=actor_id,
            role=role or self._default_role,
            created_at=created_at,
            metadata=metadata
        )
    
    def record_update_memory(
        self,
        memory_id: str,
        old_content: str,
        new_content: str,
        actor_id: Optional[str] = None,
        role: Optional[str] = None,
        memory_type: str = "user",
        embedding_model: str = "unknown",
        created_at: Optional[str] = None,
        updated_at: Optional[str] = None,
        is_async: bool = False
    ) -> str:
        """
        记录更新内存操作
        
        Args:
            memory_id: 内存记录ID
            old_content: 旧内容
            new_content: 新内容
            actor_id: 操作者ID
            role: 角色
            memory_type: 内存类型
            embedding_model: 嵌入模型名称
            created_at: 创建时间
            updated_at: 更新时间
            is_async: 是否异步操作
            
        Returns:
            str: 历史记录ID
        """
        metadata = (HistoryMetadataBuilder()
                   .set_operation_type("memory_update")
                   .set_memory_type(memory_type)
                   .set_content_info(len(new_content) if new_content else 0, embedding_model)
                   .set_update_info(
                       previous_content_length=len(old_content) if old_content else 0,
                       new_content_length=len(new_content) if new_content else 0
                   )
                   .set_performance_info(async_operation=is_async)
                   .build())
                   
        return self._add_history_record(
            memory_id=memory_id,
            old_memory=old_content,
            new_memory=new_content,
            event=HistoryEventType.UPDATE.value,
            actor_id=actor_id,
            role=role or self._default_role,
            created_at=created_at,
            updated_at=updated_at,
            metadata=metadata
        )
    
    def record_delete_memory(
        self,
        memory_id: str,
        deleted_content: str,
        actor_id: Optional[str] = None,
        role: Optional[str] = None,
        memory_type: str = "user", 
        cascade: bool = False,
        backup_created: bool = True,
        references_count: int = 0,
        created_at: Optional[str] = None,
        is_async: bool = False
    ) -> str:
        """
        记录删除内存操作
        
        Args:
            memory_id: 内存记录ID
            deleted_content: 被删除的内容
            actor_id: 操作者ID
            role: 角色
            memory_type: 内存类型
            cascade: 是否级联删除
            backup_created: 是否创建备份
            references_count: 引用计数
            created_at: 创建时间
            is_async: 是否异步操作
            
        Returns:
            str: 历史记录ID
        """
        metadata = (HistoryMetadataBuilder()
                   .set_operation_type("memory_deletion")
                   .set_memory_type(memory_type)
                   .set_content_info(len(deleted_content) if deleted_content else 0)
                   .set_delete_info(cascade, backup_created, references_count)
                   .set_performance_info(async_operation=is_async)
                   .add_custom_field("deleted_content_length", len(deleted_content) if deleted_content else 0)
                   .build())
                   
        return self._add_history_record(
            memory_id=memory_id,
            old_memory=deleted_content,
            new_memory=None,
            event=HistoryEventType.DELETE.value,
            actor_id=actor_id,
            role=role or self._default_role,
            created_at=created_at,
            is_deleted=1,
            metadata=metadata
        )
    
    def record_search_operation(
        self,
        search_id: str,
        query: str,
        actor_id: Optional[str] = None,
        role: Optional[str] = None,
        query_type: str = "semantic",
        results_count: int = 0,
        filters: Optional[Dict] = None,
        limit: Optional[int] = None,
        created_at: Optional[str] = None,
        is_async: bool = False
    ) -> str:
        """
        记录搜索操作
        
        Args:
            search_id: 搜索记录ID
            query: 搜索查询
            actor_id: 操作者ID
            role: 角色
            query_type: 查询类型
            results_count: 结果数量
            filters: 搜索过滤器
            limit: 结果限制
            created_at: 创建时间
            is_async: 是否异步操作
            
        Returns:
            str: 历史记录ID
        """
        metadata = (HistoryMetadataBuilder()
                   .set_operation_type("memory_search")
                   .set_search_info(query_type, results_count, filters, limit)
                   .set_performance_info(async_operation=is_async)
                   .add_custom_field("index_used", "vector")
                   .build())
                   
        return self._add_history_record(
            memory_id=search_id,
            old_memory=None,
            new_memory=query,
            event=HistoryEventType.SEARCH.value,
            actor_id=actor_id,
            role=role or self._infer_role_from_actor(actor_id),
            created_at=created_at,
            metadata=metadata
        )
    
    def get_memory_history(self, memory_id: str) -> List[Dict[str, Any]]:
        """
        获取内存记录的完整历史
        
        Args:
            memory_id: 内存记录ID
            
        Returns:
            List[Dict]: 历史记录列表
        """
        try:
            return self.storage.get_history(memory_id)
        except Exception as e:
            logger.error(f"Failed to get history for memory {memory_id}: {e}")
            return []
    
    def get_history_statistics(self) -> Dict[str, Any]:
        """
        获取历史记录统计信息
        
        Returns:
            Dict: 统计信息
        """
        # 这里可以添加统计查询逻辑
        # 暂时返回基本信息
        return {
            "message": "Statistics feature to be implemented",
            "available_operations": ["ADD", "UPDATE", "DELETE", "SEARCH"]
        }
    
    def _add_history_record(
        self,
        memory_id: str,
        old_memory: Optional[str],
        new_memory: Optional[str],
        event: str,
        actor_id: Optional[str] = None,
        role: Optional[str] = None,
        created_at: Optional[str] = None,
        updated_at: Optional[str] = None,
        is_deleted: int = 0,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        添加历史记录的内部方法
        
        包含完整的验证和错误处理逻辑
        """
        try:
            # 参数验证
            if not memory_id:
                raise ValueError("memory_id cannot be empty")
            
            if event not in [e.value for e in HistoryEventType]:
                raise ValueError(f"Invalid event type: {event}")
            
            # 使用存储管理器添加记录
            return self.storage.add_history(
                memory_id=memory_id,
                old_memory=old_memory,
                new_memory=new_memory,
                event=event,
                created_at=created_at,
                updated_at=updated_at,
                is_deleted=is_deleted,
                actor_id=actor_id,
                role=role or self._default_role,
                metadata=metadata
            )
            
        except Exception as e:
            logger.error(f"Failed to add history record for {memory_id}: {e}")
            raise
    
    def _infer_role_from_actor(self, actor_id: Optional[str]) -> str:
        """
        从actor_id推断角色
        
        Args:
            actor_id: 操作者ID
            
        Returns:
            str: 推断的角色
        """
        if not actor_id:
            return HistoryRole.SYSTEM.value
        
        # 简单的角色推断逻辑
        if "user" in actor_id.lower():
            return HistoryRole.USER.value
        elif "agent" in actor_id.lower():
            return HistoryRole.AGENT.value
        else:
            return HistoryRole.SYSTEM.value


class HistoryManagerFactory:
    """
    历史记录管理器工厂类
    
    实现工厂模式，简化HistoryManager的创建过程
    遵循依赖倒置原则(DIP)
    """
    
    @staticmethod
    def create_history_manager(db_path: str) -> HistoryManager:
        """
        创建历史记录管理器实例
        
        Args:
            db_path: 数据库路径
            
        Returns:
            HistoryManager: 历史记录管理器实例
        """
        try:
            storage_manager = SQLiteManager(db_path)
            return HistoryManager(storage_manager)
        except Exception as e:
            logger.error(f"Failed to create HistoryManager: {e}")
            raise
    
    @staticmethod  
    def create_from_existing_storage(storage_manager: SQLiteManager) -> HistoryManager:
        """
        从现有存储管理器创建历史记录管理器
        
        Args:
            storage_manager: 现有的存储管理器实例
            
        Returns:
            HistoryManager: 历史记录管理器实例
        """
        return HistoryManager(storage_manager)