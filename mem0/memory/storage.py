import logging
import sqlite3
import threading
import uuid
import json
from datetime import datetime
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


class SQLiteManager:
    def __init__(self, db_path: str = ":memory:"):
        self.db_path = db_path
        self.connection = sqlite3.connect(self.db_path, check_same_thread=False)
        self._lock = threading.Lock()
        self._migrate_history_table()
        self._create_history_table()

    def _migrate_history_table(self) -> None:
        """
        If a pre-existing history table had the old group-chat columns,
        rename it, create the new schema, copy the intersecting data, then
        drop the old table.
        """
        with self._lock:
            try:
                # Start a transaction
                self.connection.execute("BEGIN")
                cur = self.connection.cursor()

                cur.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='history'")
                if cur.fetchone() is None:
                    self.connection.execute("COMMIT")
                    return  # nothing to migrate

                cur.execute("PRAGMA table_info(history)")
                old_cols = {row[1] for row in cur.fetchall()}

                expected_cols = {
                    "id",
                    "memory_id",
                    "old_memory",
                    "new_memory",
                    "event",  
                    "created_at",
                    "updated_at",
                    "is_deleted",
                    "actor_id", 
                    "role",
                    "response_time",
                    "metadata",
                }

                if old_cols == expected_cols:
                    self.connection.execute("COMMIT")
                    return

                logger.info("Migrating history table to new schema (no convo columns).")

                # Clean up any existing history_old table from previous failed migration
                cur.execute("DROP TABLE IF EXISTS history_old")

                # Rename the current history table
                cur.execute("ALTER TABLE history RENAME TO history_old")

                # Create the new history table with updated schema
                cur.execute(
                    """
                    CREATE TABLE history (
                        id           TEXT PRIMARY KEY,
                        memory_id    TEXT,
                        old_memory   TEXT,
                        new_memory   TEXT,
                        event        TEXT,
                        created_at   DATETIME,
                        updated_at   DATETIME,
                        is_deleted   INTEGER,
                        actor_id     TEXT,
                        role         TEXT,
                        response_time REAL,
                        metadata     TEXT
                    )
                """
                )

                # Copy data from old table to new table
                intersecting = list(expected_cols & old_cols)
                if intersecting:
                    cols_csv = ", ".join(intersecting)
                    cur.execute(f"INSERT INTO history ({cols_csv}) SELECT {cols_csv} FROM history_old")

                # Drop the old table
                cur.execute("DROP TABLE history_old")

                # Commit the transaction
                self.connection.execute("COMMIT")
                logger.info("History table migration completed successfully.")

            except Exception as e:
                # Rollback the transaction on any error
                self.connection.execute("ROLLBACK")
                logger.error(f"History table migration failed: {e}")
                raise

    def _create_history_table(self) -> None:
        with self._lock:
            try:
                self.connection.execute("BEGIN")
                self.connection.execute(
                    """
                    CREATE TABLE IF NOT EXISTS history (
                        id           TEXT PRIMARY KEY,
                        memory_id    TEXT,
                        old_memory   TEXT,
                        new_memory   TEXT,
                        event        TEXT,
                        created_at   DATETIME,
                        updated_at   DATETIME,
                        is_deleted   INTEGER,
                        actor_id     TEXT,
                        role         TEXT,
                        response_time REAL,
                        metadata     TEXT
                    )
                """
                )
                self.connection.execute("COMMIT")
            except Exception as e:
                self.connection.execute("ROLLBACK")
                logger.error(f"Failed to create history table: {e}")
                raise

    def add_history(
        self,
        memory_id: str,
        old_memory: Optional[str],
        new_memory: Optional[str],
        event: str,
        *,
        created_at: Optional[str] = None,
        updated_at: Optional[str] = None,
        is_deleted: int = 0,
        actor_id: Optional[str] = None,
        role: Optional[str] = None,
        response_time: Optional[float] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        添加历史记录，支持完整的字段和默认值处理
        
        Args:
            memory_id: 内存记录ID
            old_memory: 旧的内存内容
            new_memory: 新的内存内容
            event: 事件类型 (ADD, UPDATE, DELETE, SEARCH)
            created_at: 创建时间，None时使用当前时间
            updated_at: 更新时间，None时使用当前时间  
            is_deleted: 是否删除标记
            actor_id: 操作者ID
            role: 角色信息
            response_time: 响应时间(秒)
            metadata: 元数据字典
            
        Returns:
            str: 历史记录ID
            
        Raises:
            ValueError: 参数验证失败
            Exception: 数据库操作失败
        """
        # 参数验证
        if not memory_id or not isinstance(memory_id, str):
            raise ValueError("memory_id must be a non-empty string")
            
        if not event or event not in ["ADD", "UPDATE", "DELETE", "SEARCH"]:
            raise ValueError("event must be one of: ADD, UPDATE, DELETE, SEARCH")
        
        # 生成历史记录ID
        history_id = str(uuid.uuid4())
        
        # 处理时间戳
        current_time = datetime.now().isoformat()
        if created_at is None:
            created_at = current_time
        if updated_at is None:
            updated_at = current_time
            
        # 处理角色默认值
        if role is None:
            role = "system"  # 提供默认角色
            
        # 处理元数据
        metadata_json = None
        if metadata is not None:
            try:
                metadata_json = json.dumps(metadata, ensure_ascii=False)
            except (TypeError, ValueError) as e:
                logger.warning(f"Failed to serialize metadata: {e}")
                metadata_json = json.dumps({"error": "serialization_failed", "original_type": str(type(metadata))})
        
        # 业务逻辑验证
        if event == "ADD" and old_memory is not None:
            logger.warning("ADD event typically should have old_memory=None")
        elif event == "DELETE" and new_memory is not None:
            logger.warning("DELETE event typically should have new_memory=None")
            
        with self._lock:
            try:
                self.connection.execute("BEGIN")
                self.connection.execute(
                    """
                    INSERT INTO history (
                        id, memory_id, old_memory, new_memory, event,
                        created_at, updated_at, is_deleted, actor_id, role,
                        response_time, metadata
                    )
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """,
                    (
                        history_id,
                        memory_id,
                        old_memory,
                        new_memory,
                        event,
                        created_at,
                        updated_at,
                        is_deleted,
                        actor_id,
                        role,
                        response_time,
                        metadata_json,
                    ),
                )
                self.connection.execute("COMMIT")
                logger.debug(f"Successfully added history record {history_id} for memory {memory_id}, event: {event}")
                return history_id
                
            except Exception as e:
                self.connection.execute("ROLLBACK")
                logger.error(f"Failed to add history record: {e}")
                raise

    def get_history(self, memory_id: str) -> List[Dict[str, Any]]:
        """
        获取指定内存记录的历史变更记录
        
        Args:
            memory_id: 内存记录ID
            
        Returns:
            List[Dict]: 历史记录列表，按时间排序
        """
        with self._lock:
            cur = self.connection.execute(
                """
                SELECT id, memory_id, old_memory, new_memory, event,
                       created_at, updated_at, is_deleted, actor_id, role,
                       response_time, metadata
                FROM history
                WHERE memory_id = ?
                ORDER BY created_at ASC, DATETIME(updated_at) ASC
            """,
                (memory_id,),
            )
            rows = cur.fetchall()

        history_records = []
        for r in rows:
            # 解析metadata
            metadata = None
            if r[11]:  # metadata字段
                try:
                    metadata = json.loads(r[11])
                except (json.JSONDecodeError, TypeError) as e:
                    logger.warning(f"Failed to parse metadata for history record {r[0]}: {e}")
                    metadata = {"parse_error": str(e), "raw_metadata": r[11]}
            
            history_records.append({
                "id": r[0],
                "memory_id": r[1],
                "old_memory": r[2],
                "new_memory": r[3],
                "event": r[4],
                "created_at": r[5],
                "updated_at": r[6],
                "is_deleted": bool(r[7]),
                "actor_id": r[8],
                "role": r[9],
                "response_time": r[10],
                "metadata": metadata,
            })
            
        return history_records

    def reset(self) -> None:
        """Drop and recreate the history table."""
        with self._lock:
            try:
                self.connection.execute("BEGIN")
                self.connection.execute("DROP TABLE IF EXISTS history")
                self.connection.execute("COMMIT")
                self._create_history_table()
            except Exception as e:
                self.connection.execute("ROLLBACK")
                logger.error(f"Failed to reset history table: {e}")
                raise

    def close(self) -> None:
        if self.connection:
            self.connection.close()
            self.connection = None

    def __del__(self):
        self.close()
