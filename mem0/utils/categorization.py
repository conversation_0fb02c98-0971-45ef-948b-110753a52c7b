"""
LLM-based memory categorization module
Based on OpenMemory's categorization system
"""
import logging
from typing import List
from pydantic import BaseModel
from tenacity import retry, stop_after_attempt, wait_exponential

logger = logging.getLogger(__name__)

# Memory categorization prompt based on OpenMemory's system
MEMORY_CATEGORIZATION_PROMPT = """Your task is to assign each piece of information (or "memory") to one or more of the following categories. Feel free to use multiple categories per item when appropriate.

- Personal: family, friends, home, hobbies, lifestyle
- Relationships: social network, significant others, colleagues
- Preferences: likes, dislikes, habits, favorite media
- Health: physical fitness, mental health, diet, sleep
- Travel: trips, commutes, favorite places, itineraries
- Work: job roles, companies, projects, promotions
- Education: courses, degrees, certifications, skills development
- Projects: to‑dos, milestones, deadlines, status updates
- AI, ML & Technology: infrastructure, algorithms, tools, research
- Technical Support: bug reports, error logs, fixes
- Finance: income, expenses, investments, billing
- Shopping: purchases, wishlists, returns, deliveries
- Legal: contracts, policies, regulations, privacy
- Entertainment: movies, music, games, books, events
- Messages: emails, SMS, alerts, reminders
- Customer Support: tickets, inquiries, resolutions
- Product Feedback: ratings, bug reports, feature requests
- News: articles, headlines, trending topics
- Organization: meetings, appointments, calendars
- Goals: ambitions, KPIs, long‑term objectives

Guidelines:
- Return only the categories under 'categories' key in the JSON format.
- If you cannot categorize the memory, return an empty list with key 'categories'.
- Don't limit yourself to the categories listed above only. Feel free to create new categories based on the memory. Make sure that it is a single phrase.
"""


class MemoryCategories(BaseModel):
    categories: List[str]


@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=15))
def get_categories_for_memory(memory: str, llm_instance=None) -> List[str]:
    """
    Get categories for a memory using LLM structured output.
    
    Args:
        memory (str): The memory text to categorize
        llm_instance: The LLM instance to use for categorization
        
    Returns:
        List[str]: List of category names (lowercase, stripped)
    """
    if not llm_instance:
        logger.warning("No LLM instance provided for categorization, returning empty categories")
        return []
    
    try:
        messages = [
            {"role": "system", "content": MEMORY_CATEGORIZATION_PROMPT},
            {"role": "user", "content": memory}
        ]

        # Generate response using the LLM instance
        response = llm_instance.generate_response(
            messages=messages,
            response_format={"type": "json_object"},
        )
        
        # Parse the JSON response
        import json
        from mem0.memory.utils import remove_code_blocks
        
        response = remove_code_blocks(response)
        parsed_response = json.loads(response)
        
        if isinstance(parsed_response, dict) and "categories" in parsed_response:
            categories = parsed_response["categories"]
            if isinstance(categories, list):
                # Return categories as lowercase, stripped strings
                return [cat.strip().lower() for cat in categories if cat and cat.strip()]
        
        logger.warning(f"Invalid categorization response format: {parsed_response}")
        return []

    except Exception as e:
        logger.error(f"[ERROR] Failed to get categories: {e}")
        logger.debug(f"[DEBUG] Memory content: {memory[:100]}...")
        return []


def categorize_memory_if_enabled(memory_text: str, llm_instance=None, custom_categories=None) -> List[str]:
    """
    Categorize a memory using LLM if no custom categories are provided.
    
    Args:
        memory_text (str): The memory text to categorize
        llm_instance: The LLM instance to use for categorization
        custom_categories: Pre-defined custom categories (takes precedence)
        
    Returns:
        List[str]: List of category names
    """
    # If custom categories are provided, use them
    if custom_categories:
        categories_list = []
        for cat_dict in custom_categories:
            if isinstance(cat_dict, dict):
                # Extract category names from the dictionary
                categories_list.extend(cat_dict.keys())
            elif isinstance(cat_dict, str):
                categories_list.append(cat_dict)
        return [cat.strip().lower() for cat in categories_list if cat and cat.strip()]
    
    # Otherwise, use LLM for automatic categorization
    if llm_instance and memory_text and memory_text.strip():
        return get_categories_for_memory(memory_text.strip(), llm_instance)
    
    return []